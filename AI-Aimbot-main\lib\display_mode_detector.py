"""
Display Mode Detection System
Implements technical concepts from AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md
Detects fullscreen exclusive vs windowed/borderless modes and provides optimization recommendations
"""

import ctypes
import ctypes.wintypes
import time
from dataclasses import dataclass
from enum import Enum
from typing import Optional, Tuple, List
import win32gui
import win32con
import win32api


class DisplayMode(Enum):
    """Display mode types as described in technical documentation"""
    FULLSCREEN_EXCLUSIVE = "fullscreen_exclusive"
    BORDERLESS_WINDOWED = "borderless_windowed"  
    WINDOWED = "windowed"
    UNKNOWN = "unknown"


@dataclass
class DisplayInfo:
    """Display mode information"""
    mode: DisplayMode
    window_handle: Optional[int] = None
    window_title: str = ""
    window_class: str = ""
    resolution: Tuple[int, int] = (0, 0)
    position: Tuple[int, int] = (0, 0)
    is_capture_compatible: bool = False
    performance_impact: str = "unknown"
    recommendations: List[str] = None

    def __post_init__(self):
        if self.recommendations is None:
            self.recommendations = []


class DisplayModeDetector:
    """
    Detects display modes and provides technical analysis
    Based on concepts from AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md
    """
    
    def __init__(self, logger):
        self.logger = logger
        self.screen_width = ctypes.windll.user32.GetSystemMetrics(0)
        self.screen_height = ctypes.windll.user32.GetSystemMetrics(1)
        self.logger.info(f"Screen resolution: {self.screen_width}x{self.screen_height}")
    
    def detect_foreground_display_mode(self) -> DisplayInfo:
        """
        Detect display mode of foreground window
        Implements technical analysis from documentation
        """
        try:
            # Get foreground window
            hwnd = win32gui.GetForegroundWindow()
            if not hwnd:
                return DisplayInfo(DisplayMode.UNKNOWN)
            
            # Get window information
            window_title = win32gui.GetWindowText(hwnd)
            window_class = win32gui.GetClassName(hwnd)
            
            # Get window rectangle
            try:
                window_rect = win32gui.GetWindowRect(hwnd)
                client_rect = win32gui.GetClientRect(hwnd)
            except Exception as e:
                self.logger.error(f"Failed to get window rectangles: {e}")
                return DisplayInfo(DisplayMode.UNKNOWN)
            
            # Calculate window properties
            window_width = window_rect[2] - window_rect[0]
            window_height = window_rect[3] - window_rect[1]
            window_x = window_rect[0]
            window_y = window_rect[1]
            
            client_width = client_rect[2] - client_rect[0]
            client_height = client_rect[3] - client_rect[1]
            
            # Analyze display mode
            display_info = self._analyze_display_mode(
                hwnd, window_title, window_class,
                window_x, window_y, window_width, window_height,
                client_width, client_height
            )
            
            # Add technical recommendations
            self._add_technical_recommendations(display_info)
            
            return display_info
            
        except Exception as e:
            self.logger.error(f"Display mode detection failed: {e}")
            return DisplayInfo(DisplayMode.UNKNOWN)
    
    def _analyze_display_mode(self, hwnd: int, title: str, class_name: str,
                             win_x: int, win_y: int, win_width: int, win_height: int,
                             client_width: int, client_height: int) -> DisplayInfo:
        """Analyze window properties to determine display mode"""
        
        # Check for fullscreen exclusive mode indicators
        if self._is_fullscreen_exclusive(hwnd, win_x, win_y, win_width, win_height):
            return DisplayInfo(
                mode=DisplayMode.FULLSCREEN_EXCLUSIVE,
                window_handle=hwnd,
                window_title=title,
                window_class=class_name,
                resolution=(win_width, win_height),
                position=(win_x, win_y),
                is_capture_compatible=False,
                performance_impact="optimal"
            )
        
        # Check for borderless windowed mode
        elif self._is_borderless_windowed(win_x, win_y, win_width, win_height, client_width, client_height):
            return DisplayInfo(
                mode=DisplayMode.BORDERLESS_WINDOWED,
                window_handle=hwnd,
                window_title=title,
                window_class=class_name,
                resolution=(win_width, win_height),
                position=(win_x, win_y),
                is_capture_compatible=True,
                performance_impact="minimal"
            )
        
        # Regular windowed mode
        else:
            return DisplayInfo(
                mode=DisplayMode.WINDOWED,
                window_handle=hwnd,
                window_title=title,
                window_class=class_name,
                resolution=(win_width, win_height),
                position=(win_x, win_y),
                is_capture_compatible=True,
                performance_impact="moderate"
            )
    
    def _is_fullscreen_exclusive(self, hwnd: int, x: int, y: int, width: int, height: int) -> bool:
        """
        Detect fullscreen exclusive mode
        Based on technical characteristics from documentation
        """
        try:
            # Check if window covers entire screen
            covers_screen = (x <= 0 and y <= 0 and 
                           width >= self.screen_width and 
                           height >= self.screen_height)
            
            if not covers_screen:
                return False
            
            # Check window styles for fullscreen exclusive indicators
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
            ex_style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            
            # Fullscreen exclusive typically has minimal window styles
            has_minimal_style = not (style & win32con.WS_CAPTION)
            has_topmost = bool(ex_style & win32con.WS_EX_TOPMOST)
            
            # Additional checks for exclusive mode
            try:
                # Check if window bypasses DWM (Windows Vista+)
                dwm_enabled = self._is_dwm_composition_enabled()
                if dwm_enabled:
                    # In fullscreen exclusive, DWM composition might be disabled for this window
                    pass  # This is complex to detect reliably
            except:
                pass
            
            return has_minimal_style and covers_screen
            
        except Exception as e:
            self.logger.error(f"Fullscreen exclusive detection failed: {e}")
            return False
    
    def _is_borderless_windowed(self, x: int, y: int, width: int, height: int,
                               client_width: int, client_height: int) -> bool:
        """
        Detect borderless windowed mode
        Window covers screen but has no borders/decorations
        """
        # Covers entire screen
        covers_screen = (x <= 0 and y <= 0 and 
                        width >= self.screen_width and 
                        height >= self.screen_height)
        
        # Client area matches window area (no borders)
        no_borders = (abs(client_width - width) <= 2 and 
                     abs(client_height - height) <= 2)
        
        return covers_screen and no_borders
    
    def _is_dwm_composition_enabled(self) -> bool:
        """Check if Desktop Window Manager composition is enabled"""
        try:
            # Load dwmapi.dll
            dwmapi = ctypes.windll.dwmapi
            enabled = ctypes.c_bool()
            result = dwmapi.DwmIsCompositionEnabled(ctypes.byref(enabled))
            return result == 0 and enabled.value
        except:
            return True  # Assume enabled on modern Windows
    
    def _add_technical_recommendations(self, display_info: DisplayInfo):
        """Add technical recommendations based on display mode"""
        
        if display_info.mode == DisplayMode.FULLSCREEN_EXCLUSIVE:
            display_info.recommendations.extend([
                "CRITICAL: Fullscreen exclusive mode detected",
                "Screen capture will likely fail or return black frames",
                "Switch to borderless windowed mode for aimbot compatibility",
                "Performance: Optimal gaming performance but blocks external tools",
                "Technical: Direct hardware access bypasses DWM compositor"
            ])
        
        elif display_info.mode == DisplayMode.BORDERLESS_WINDOWED:
            display_info.recommendations.extend([
                "OPTIMAL: Borderless windowed mode detected",
                "Full screen capture compatibility",
                "Minimal performance impact (~1-5% overhead)",
                "Recommended mode for AI aimbot operation",
                "Technical: Rendered through DWM, allows capture APIs"
            ])
        
        elif display_info.mode == DisplayMode.WINDOWED:
            display_info.recommendations.extend([
                "COMPATIBLE: Windowed mode detected",
                "Screen capture compatible with coordinate translation",
                "Moderate performance overhead from window management",
                "Consider borderless windowed for better experience",
                "Technical: Full DWM compositing with window decorations"
            ])
        
        else:
            display_info.recommendations.extend([
                "WARNING: Could not determine display mode",
                "Screen capture compatibility unknown",
                "Monitor application behavior for issues"
            ])
    
    def get_capture_compatibility_report(self, display_info: DisplayInfo) -> str:
        """Generate detailed compatibility report"""
        
        report = f"""
=== DISPLAY MODE ANALYSIS ===
Mode: {display_info.mode.value.upper()}
Window: {display_info.window_title}
Class: {display_info.window_class}
Resolution: {display_info.resolution[0]}x{display_info.resolution[1]}
Position: ({display_info.position[0]}, {display_info.position[1]})
Capture Compatible: {'YES' if display_info.is_capture_compatible else 'NO'}
Performance Impact: {display_info.performance_impact.upper()}

=== TECHNICAL ANALYSIS ===
"""
        
        for recommendation in display_info.recommendations:
            report += f"• {recommendation}\n"
        
        report += f"""
=== SCREEN CAPTURE APIS ===
BitBlt: {'Compatible' if display_info.is_capture_compatible else 'BLOCKED'}
PrintWindow: {'Compatible' if display_info.is_capture_compatible else 'BLOCKED'}
DXGI Desktop Duplication: {'Compatible' if display_info.is_capture_compatible else 'BLOCKED'}
MSS Library: {'Compatible' if display_info.is_capture_compatible else 'BLOCKED'}

=== RECOMMENDATIONS ===
"""
        
        if display_info.mode == DisplayMode.FULLSCREEN_EXCLUSIVE:
            report += "1. Switch game to borderless windowed mode\n"
            report += "2. Check game graphics settings for 'Windowed Fullscreen'\n"
            report += "3. Verify screen capture works after mode change\n"
        elif display_info.mode == DisplayMode.BORDERLESS_WINDOWED:
            report += "1. Current mode is optimal for aimbot operation\n"
            report += "2. No changes needed\n"
        else:
            report += "1. Consider borderless windowed for best experience\n"
            report += "2. Current mode should work but may need coordinate adjustment\n"
        
        return report
    
    def monitor_display_mode_changes(self, callback, interval: float = 1.0):
        """Monitor for display mode changes"""
        last_mode = None
        
        while True:
            try:
                current_info = self.detect_foreground_display_mode()
                
                if last_mode != current_info.mode:
                    self.logger.info(f"Display mode changed: {last_mode} -> {current_info.mode}")
                    callback(current_info)
                    last_mode = current_info.mode
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                self.logger.error(f"Display mode monitoring error: {e}")
                time.sleep(interval)
