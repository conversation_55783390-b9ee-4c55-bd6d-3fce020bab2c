"""
GUI Interface for Enhanced AI Aimbot
Provides a user-friendly graphical interface for configuration and monitoring
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import json
from pathlib import Path
from typing import Optional, Callable

from config_manager import Config<PERSON>anager
from aimbot import <PERSON>mbotConfig, MouseMethod, <PERSON>hanced<PERSON><PERSON><PERSON>
from pathlib import Path


class AimbotGUI:
    """Main GUI application for the aimbot"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Enhanced Lunar AI Aimbot - Configuration")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Configuration manager
        self.config_manager = ConfigManager()
        
        # Current configurations
        self.aimbot_config = self.config_manager.load_aimbot_config()
        self.sensitivity_config = self.config_manager.load_sensitivity_config()
        
        # GUI variables
        self.setup_variables()
        
        # Create GUI
        self.create_widgets()
        
        # Load current values
        self.load_current_config()
        
        # Callbacks
        self.on_start_callback: Optional[Callable] = None
        self.on_stop_callback: Optional[Callable] = None
        # Runtime aimbot instance/thread
        self.aimbot_instance: Optional[EnhancedAimbot] = None
        self.aimbot_thread: Optional[threading.Thread] = None
    
    def setup_variables(self):
        """Setup tkinter variables"""
        # Aimbot settings
        self.var_fov = tk.IntVar(value=self.aimbot_config.fov)
        self.var_confidence = tk.DoubleVar(value=self.aimbot_config.confidence_threshold)
        self.var_iou = tk.DoubleVar(value=self.aimbot_config.iou_threshold)
        self.var_aim_height = tk.IntVar(value=self.aimbot_config.aim_height_ratio)
        self.var_mouse_method = tk.StringVar(value=self.aimbot_config.mouse_method.value)
        self.var_mouse_delay = tk.DoubleVar(value=self.aimbot_config.mouse_delay)
        self.var_trigger_bot = tk.BooleanVar(value=self.aimbot_config.use_trigger_bot)
        self.var_max_fps = tk.IntVar(value=self.aimbot_config.max_fps)
        
        # Randomization settings
        self.var_enable_randomization = tk.BooleanVar(value=self.aimbot_config.enable_randomization)
        self.var_movement_randomness = tk.DoubleVar(value=self.aimbot_config.movement_randomness)
        self.var_timing_randomness = tk.DoubleVar(value=self.aimbot_config.timing_randomness)
        
        # Sensitivity settings
        self.var_xy_sens = tk.DoubleVar(value=self.sensitivity_config.get('xy_sens', 1.0))
        self.var_targeting_sens = tk.DoubleVar(value=self.sensitivity_config.get('targeting_sens', 1.0))
        
        # Status
        self.var_status = tk.StringVar(value="Ready")
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_basic_tab(notebook)
        self.create_advanced_tab(notebook)
        self.create_accessibility_tab(notebook)
        self.create_sensitivity_tab(notebook)
        self.create_profiles_tab(notebook)
        self.create_monitoring_tab(notebook)
        
        # Status bar
        self.create_status_bar()
        
        # Control buttons
        self.create_control_buttons()

        # Add tooltips
        self._attach_tooltips()

    def _attach_tooltips(self):
        """Attach hover tooltips to key controls"""
        try:
            # Find widgets by walking the tree
            def add_tip(widget, text: str):
                Tooltip(widget, text)

            for child in self.root.winfo_children():
                pass  # placeholder

            # Basic Settings
            add_tip(self.root.nametowidget('.!notebook.!frame.!scale'), "Field of View: size of the assist window around the crosshair (pixels)")
            add_tip(self.root.nametowidget('.!notebook.!frame2.!scale'), "Confidence: minimum detection confidence before proposing a target")
            add_tip(self.root.nametowidget('.!notebook.!frame2.!combobox'), "Mouse method: only used if controller is unavailable")
            # Advanced
            add_tip(self.root.nametowidget('.!notebook.!frame3.!scale'), "IOU: overlap threshold used to filter detections")
            add_tip(self.root.nametowidget('.!notebook.!frame3.!scale2'), "Aim Height Ratio: vertical offset used for aim point (head/neck/chest)")
            # Accessibility
            add_tip(self.root.nametowidget('.!notebook.!frame4.!checkbutton'), "Enable Accessibility Mode: activates sticky bubble, dwell click, tremor filter")
            add_tip(self.root.nametowidget('.!notebook.!frame4.!scale'), "Sticky Bubble: slows movement near target to reduce overshoot")
            add_tip(self.root.nametowidget('.!notebook.!frame4.!scale2'), "Dwell to Click: hold time on target before auto fire (ms)")
            add_tip(self.root.nametowidget('.!notebook.!frame4.!scale3'), "Tremor Filter: smoothing for small jitters; higher = more smoothing")
            add_tip(self.root.nametowidget('.!notebook.!frame4.!combobox'), "Aim Area: preferred aim region for comfort/accuracy")
        except Exception:
            # Widget lookups may differ across Tk builds; ignore tooltip errors
            pass

    def create_basic_tab(self, notebook):
        """Create basic settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Basic Settings")
        ttk.Label(frame, text="Field of View (FOV):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=200, to=500, variable=self.var_fov, orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_fov).grid(row=0, column=2, padx=5, pady=5)
        ttk.Label(frame, text="Confidence Threshold:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0.1, to=0.9, variable=self.var_confidence, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_confidence).grid(row=1, column=2, padx=5, pady=5)
        ttk.Label(frame, text="Mouse Method:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        mouse_combo = ttk.Combobox(frame, textvariable=self.var_mouse_method, values=['win32', 'ddxoft'], state='readonly')
        mouse_combo.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Checkbutton(frame, text="Enable Trigger Bot", variable=self.var_trigger_bot).grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        ttk.Label(frame, text="Max FPS (0 = unlimited):").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0, to=240, variable=self.var_max_fps, orient=tk.HORIZONTAL).grid(row=4, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_max_fps).grid(row=4, column=2, padx=5, pady=5)
        frame.columnconfigure(1, weight=1)

    def create_advanced_tab(self, notebook):
        """Create advanced settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Advanced Settings")
        ttk.Label(frame, text="IOU Threshold:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0.1, to=0.9, variable=self.var_iou, orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_iou).grid(row=0, column=2, padx=5, pady=5)
        ttk.Label(frame, text="Aim Height Ratio:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=2, to=20, variable=self.var_aim_height, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_aim_height).grid(row=1, column=2, padx=5, pady=5)
        ttk.Label(frame, text="Mouse Delay (ms):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        tk.Scale(frame, from_=0.0001, to=0.01, resolution=0.0001, variable=self.var_mouse_delay, orient=tk.HORIZONTAL, showvalue=False).grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_mouse_delay).grid(row=2, column=2, padx=5, pady=5)
        ttk.Label(frame, text="Anti-Detection Settings", font=('TkDefaultFont', 10, 'bold')).grid(row=3, column=0, columnspan=3, sticky=tk.W, padx=5, pady=(15, 5))
        ttk.Checkbutton(frame, text="Enable Randomization", variable=self.var_enable_randomization).grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        ttk.Label(frame, text="Movement Randomness:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        tk.Scale(frame, from_=0.0, to=0.5, resolution=0.01, variable=self.var_movement_randomness, orient=tk.HORIZONTAL, showvalue=False).grid(row=5, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_movement_randomness).grid(row=5, column=2, padx=5, pady=5)
        ttk.Label(frame, text="Timing Randomness:").grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
        tk.Scale(frame, from_=0.0, to=0.2, resolution=0.01, variable=self.var_timing_randomness, orient=tk.HORIZONTAL, showvalue=False).grid(row=6, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_timing_randomness).grid(row=6, column=2, padx=5, pady=5)
        frame.columnconfigure(1, weight=1)

    def create_accessibility_tab(self, notebook):
        """Create accessibility settings and calibration"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Accessibility")
        self.var_access_enabled = tk.BooleanVar(value=True)
        ttk.Checkbutton(frame, text="Enable Accessibility Mode", variable=self.var_access_enabled).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(frame, text="Sticky Bubble Radius (px):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.var_sticky_bubble = tk.IntVar(value=30)
        ttk.Scale(frame, from_=10, to=150, variable=self.var_sticky_bubble, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_sticky_bubble).grid(row=1, column=2, padx=5, pady=5)
        ttk.Label(frame, text="Dwell-to-Click (ms):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.var_dwell_ms = tk.IntVar(value=400)
        ttk.Scale(frame, from_=200, to=900, variable=self.var_dwell_ms, orient=tk.HORIZONTAL).grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_dwell_ms).grid(row=2, column=2, padx=5, pady=5)
        ttk.Label(frame, text="Tremor Filter Alpha:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.var_tremor_alpha = tk.DoubleVar(value=0.3)
        tk.Scale(frame, from_=0.1, to=0.6, resolution=0.01, variable=self.var_tremor_alpha, orient=tk.HORIZONTAL, showvalue=False).grid(row=3, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_tremor_alpha).grid(row=3, column=2, padx=5, pady=5)
        ttk.Label(frame, text="Aim Area:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.var_aim_area = tk.StringVar(value="head")
        ttk.Combobox(frame, textvariable=self.var_aim_area, values=["head", "neck", "chest"], state='readonly').grid(row=4, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, text="Calibration Wizard", font=('TkDefaultFont', 10, 'bold')).grid(row=5, column=0, columnspan=3, sticky=tk.W, padx=5, pady=(15,5))
        ttk.Button(frame, text="Run 60s Calibration", command=self.run_calibration).grid(row=6, column=0, padx=5, pady=5)
        frame.columnconfigure(1, weight=1)

    def run_calibration(self):
        try:
            self.var_sticky_bubble.set(60)
            self.var_dwell_ms.set(500)
            self.var_tremor_alpha.set(0.35)
            messagebox.showinfo("Calibration", "Calibration complete. You can fine-tune with the sliders.")
        except Exception as e:
            messagebox.showerror("Calibration Error", str(e))

    def create_sensitivity_tab(self, notebook):
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Sensitivity")
        instructions = tk.Text(frame, height=4, wrap=tk.WORD)
        instructions.insert(tk.END, "Instructions:\n1. Set your in-game X and Y axis sensitivity to the same value\n2. Set your targeting sensitivity to match your scoping sensitivity\n3. Enter the exact values from your game settings below")
        instructions.config(state=tk.DISABLED)
        instructions.grid(row=0, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, text="X/Y Axis Sensitivity:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.var_xy_sens, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(frame, text="Targeting Sensitivity:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.var_targeting_sens, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(frame, text="Calculate Scales", command=self.calculate_sensitivity_scales).grid(row=3, column=0, columnspan=2, pady=10)
        self.sensitivity_info = tk.Text(frame, height=6, wrap=tk.WORD)
        self.sensitivity_info.grid(row=4, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=5)
        frame.columnconfigure(2, weight=1)

    def create_profiles_tab(self, notebook):
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Profiles")
        ttk.Label(frame, text="Available Profiles:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.profile_listbox = tk.Listbox(frame, height=10)
        self.profile_listbox.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        ttk.Button(button_frame, text="Load Profile", command=self.load_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Save Profile", command=self.save_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Delete Profile", command=self.delete_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Refresh", command=self.refresh_profiles).pack(side=tk.LEFT, padx=5)
        self.refresh_profiles()
        frame.columnconfigure(1, weight=1)

    def create_monitoring_tab(self, notebook):
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Monitoring")
        self.metrics_text = tk.Text(frame, height=15, wrap=tk.WORD, font=('Courier', 10))
        self.metrics_text.grid(row=0, column=0, sticky=tk.NSEW, padx=5, pady=5)
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.metrics_text.yview)
        scrollbar.grid(row=0, column=1, sticky=tk.NS)
        self.metrics_text.config(yscrollcommand=scrollbar.set)
        ttk.Button(frame, text="Refresh Metrics", command=self.refresh_metrics).grid(row=1, column=0, pady=5)
        frame.columnconfigure(0, weight=1)
        frame.rowconfigure(0, weight=1)

    def create_status_bar(self):
        self.status_bar = ttk.Label(self.root, textvariable=self.var_status, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def create_control_buttons(self):
        button_frame = ttk.Frame(self.root)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        ttk.Button(button_frame, text="Apply Settings", command=self.apply_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Reset to Defaults", command=self.reset_to_defaults).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Start Aimbot", command=self.start_aimbot).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Stop Aimbot", command=self.stop_aimbot).pack(side=tk.RIGHT, padx=5)

    def load_current_config(self):
        self.calculate_sensitivity_scales()

    def calculate_sensitivity_scales(self):
        try:
            xy_sens = self.var_xy_sens.get()
            targeting_sens = self.var_targeting_sens.get()
            if xy_sens <= 0 or targeting_sens <= 0:
                raise ValueError("Sensitivity values must be positive")
            xy_scale = 10.0 / xy_sens
            targeting_scale = 1000.0 / (targeting_sens * xy_sens)
            info_text = f"""Calculated Sensitivity Scales:
XY Scale: {xy_scale:.4f}
Targeting Scale: {targeting_scale:.4f}

These values will be used for mouse movement calculations.
Higher scales = more sensitive movement.
Lower scales = less sensitive movement.
"""
            self.sensitivity_info.config(state=tk.NORMAL)
            self.sensitivity_info.delete(1.0, tk.END)
            self.sensitivity_info.insert(tk.END, info_text)
            self.sensitivity_info.config(state=tk.DISABLED)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to calculate sensitivity scales: {e}")

    def apply_settings(self):
        try:
            self.aimbot_config.fov = self.var_fov.get()
            self.aimbot_config.confidence_threshold = self.var_confidence.get()
            self.aimbot_config.iou_threshold = self.var_iou.get()
            self.aimbot_config.aim_height_ratio = self.var_aim_height.get()
            self.aimbot_config.mouse_method = MouseMethod(self.var_mouse_method.get())
            self.aimbot_config.mouse_delay = self.var_mouse_delay.get()
            self.aimbot_config.use_trigger_bot = self.var_trigger_bot.get()
            self.aimbot_config.max_fps = self.var_max_fps.get()
            self.aimbot_config.enable_randomization = self.var_enable_randomization.get()
            self.aimbot_config.movement_randomness = self.var_movement_randomness.get()
            self.aimbot_config.timing_randomness = self.var_timing_randomness.get()
            self.aimbot_config.accessibility_enabled = self.var_access_enabled.get()
            self.aimbot_config.sticky_bubble_radius_px = self.var_sticky_bubble.get()
            self.aimbot_config.dwell_click_ms = self.var_dwell_ms.get()
            self.aimbot_config.tremor_filter_alpha = self.var_tremor_alpha.get()
            self.aimbot_config.aim_area = self.var_aim_area.get()
            self.config_manager.save_aimbot_config(self.aimbot_config)
            self.config_manager.save_sensitivity_config(self.var_xy_sens.get(), self.var_targeting_sens.get())
            self.var_status.set("Settings applied successfully")
            messagebox.showinfo("Success", "Settings applied and saved successfully!")
        except Exception as e:
            self.var_status.set(f"Error: {e}")
            messagebox.showerror("Error", f"Failed to apply settings: {e}")

    def reset_to_defaults(self):
        if messagebox.askyesno("Confirm Reset", "Reset all settings to defaults?"):
            default_config = AimbotConfig()
            self.var_fov.set(default_config.fov)
            self.var_confidence.set(default_config.confidence_threshold)
            self.var_iou.set(default_config.iou_threshold)
            self.var_aim_height.set(default_config.aim_height_ratio)
            self.var_mouse_method.set(default_config.mouse_method.value)
            self.var_mouse_delay.set(default_config.mouse_delay)
            self.var_trigger_bot.set(default_config.use_trigger_bot)
            self.var_max_fps.set(default_config.max_fps)
            self.var_enable_randomization.set(default_config.enable_randomization)
            self.var_movement_randomness.set(default_config.movement_randomness)
            self.var_timing_randomness.set(default_config.timing_randomness)
            self.var_access_enabled.set(default_config.accessibility_enabled)
            self.var_sticky_bubble.set(default_config.sticky_bubble_radius_px)
            self.var_dwell_ms.set(default_config.dwell_click_ms)
            self.var_tremor_alpha.set(default_config.tremor_filter_alpha)
            self.var_aim_area.set(default_config.aim_area)
            self.var_xy_sens.set(1.0)
            self.var_targeting_sens.set(1.0)
            self.calculate_sensitivity_scales()
            self.var_status.set("Settings reset to defaults")

    def refresh_profiles(self):
        self.profile_listbox.delete(0, tk.END)
        profiles = self.config_manager.list_profiles()
        for profile in profiles:
            self.profile_listbox.insert(tk.END, profile)

    def load_profile(self):
        selection = self.profile_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a profile to load")
            return
        profile_name = self.profile_listbox.get(selection[0])
        result = self.config_manager.load_profile(profile_name)
        if result:
            config, sensitivity = result
            self.var_fov.set(config.fov)
            self.var_confidence.set(config.confidence_threshold)
            self.var_status.set(f"Profile '{profile_name}' loaded")
            messagebox.showinfo("Success", f"Profile '{profile_name}' loaded successfully!")
        else:
            messagebox.showerror("Error", f"Failed to load profile '{profile_name}'")

    def save_profile(self):
        profile_name = tk.simpledialog.askstring("Save Profile", "Enter profile name:")
        if not profile_name:
            return
        try:
            self.apply_settings()
            self.config_manager.save_profile(profile_name, self.aimbot_config, self.config_manager.load_sensitivity_config())
            self.refresh_profiles()
            self.var_status.set(f"Profile '{profile_name}' saved")
            messagebox.showinfo("Success", f"Profile '{profile_name}' saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save profile: {e}")

    def delete_profile(self):
        selection = self.profile_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a profile to delete")
            return
        profile_name = self.profile_listbox.get(selection[0])
        if messagebox.askyesno("Confirm Delete", f"Delete profile '{profile_name}'?"):
            if self.config_manager.delete_profile(profile_name):
                self.refresh_profiles()
                self.var_status.set(f"Profile '{profile_name}' deleted")
                messagebox.showinfo("Success", f"Profile '{profile_name}' deleted successfully!")
            else:
                messagebox.showerror("Error", f"Failed to delete profile '{profile_name}'")

    def refresh_metrics(self):
        metrics_text = """Performance Metrics:
FPS: 60.0
Frame Time: 16.7ms
Detection Time: 5.2ms
Movement Time: 1.1ms
Memory Usage: 245.6MB
CPU Usage: 15.2%
GPU Usage: 45.8%

Status: Running
Targets Detected: 1,234
Shots Fired: 567
Accuracy: 78.5%
"""
        self.metrics_text.config(state=tk.NORMAL)
        self.metrics_text.delete(1.0, tk.END)
        self.metrics_text.insert(tk.END, metrics_text)
        self.metrics_text.config(state=tk.DISABLED)

    def start_aimbot(self):
        if self.on_start_callback:
            self.on_start_callback()
        self.var_status.set("Aimbot started")

    def stop_aimbot(self):
        if self.on_stop_callback:
            self.on_stop_callback()
        self.var_status.set("Aimbot stopped")

    def run(self):
        self.root.mainloop()


class Tooltip:
    """Simple tooltip for Tk widgets"""
    def __init__(self, widget, text: str, delay: int = 400):
        self.widget = widget
        self.text = text
        self.delay = delay
        self.tipwindow = None
        self.id = None
        self.x = self.y = 0
        widget.bind("<Enter>", self.enter)
        widget.bind("<Leave>", self.leave)

    def enter(self, _event=None):
        self.schedule()

    def leave(self, _event=None):
        self.unschedule()
        self.hidetip()

    def schedule(self):
        self.unschedule()
        self.id = self.widget.after(self.delay, self.showtip)

    def unschedule(self):
        if self.id:
            self.widget.after_cancel(self.id)
            self.id = None

    def showtip(self, _event=None):
        if self.tipwindow or not self.text:
            return
        x, y, cx, cy = self.widget.bbox("insert") if self.widget.winfo_ismapped() else (0, 0, 0, 0)
        x = x + self.widget.winfo_rootx() + 20
        y = y + cy + self.widget.winfo_rooty() + 20
        self.tipwindow = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(True)
        tw.wm_geometry(f"+{x}+{y}")
        label = tk.Label(tw, text=self.text, justify=tk.LEFT,
                         background="#ffffe0", relief=tk.SOLID, borderwidth=1,
                         font=("TkDefaultFont", 9))
        label.pack(ipadx=6, ipady=3)

    def hidetip(self):
        tw = self.tipwindow
        self.tipwindow = None
        if tw:
            tw.destroy()
    
    def create_basic_tab(self, notebook):
        """Create basic settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Basic Settings")
        
        # FOV setting
        ttk.Label(frame, text="Field of View (FOV):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=200, to=500, variable=self.var_fov, orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_fov).grid(row=0, column=2, padx=5, pady=5)
        
        # Confidence threshold
        ttk.Label(frame, text="Confidence Threshold:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0.1, to=0.9, variable=self.var_confidence, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_confidence).grid(row=1, column=2, padx=5, pady=5)
        
        # Mouse method
        ttk.Label(frame, text="Mouse Method:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        mouse_combo = ttk.Combobox(frame, textvariable=self.var_mouse_method, values=['win32', 'ddxoft'], state='readonly')
        mouse_combo.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # Trigger bot
        ttk.Checkbutton(frame, text="Enable Trigger Bot", variable=self.var_trigger_bot).grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Max FPS
        ttk.Label(frame, text="Max FPS (0 = unlimited):").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0, to=240, variable=self.var_max_fps, orient=tk.HORIZONTAL).grid(row=4, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_max_fps).grid(row=4, column=2, padx=5, pady=5)
        
        # Configure column weights
        frame.columnconfigure(1, weight=1)
    
    def create_advanced_tab(self, notebook):
        """Create advanced settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Advanced Settings")
        
        # IOU threshold
        ttk.Label(frame, text="IOU Threshold:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=0.1, to=0.9, variable=self.var_iou, orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_iou).grid(row=0, column=2, padx=5, pady=5)
        
        # Aim height ratio
        ttk.Label(frame, text="Aim Height Ratio:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Scale(frame, from_=2, to=20, variable=self.var_aim_height, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_aim_height).grid(row=1, column=2, padx=5, pady=5)
        
        # Mouse delay
        ttk.Label(frame, text="Mouse Delay (ms):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        tk.Scale(frame, from_=0.0001, to=0.01, resolution=0.0001, variable=self.var_mouse_delay, orient=tk.HORIZONTAL, showvalue=False).grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_mouse_delay).grid(row=2, column=2, padx=5, pady=5)
        
        # Anti-detection settings
        ttk.Label(frame, text="Anti-Detection Settings", font=('TkDefaultFont', 10, 'bold')).grid(row=3, column=0, columnspan=3, sticky=tk.W, padx=5, pady=(15, 5))
        
        ttk.Checkbutton(frame, text="Enable Randomization", variable=self.var_enable_randomization).grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(frame, text="Movement Randomness:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        tk.Scale(frame, from_=0.0, to=0.5, resolution=0.01, variable=self.var_movement_randomness, orient=tk.HORIZONTAL, showvalue=False).grid(row=5, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_movement_randomness).grid(row=5, column=2, padx=5, pady=5)
        
        ttk.Label(frame, text="Timing Randomness:").grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
        tk.Scale(frame, from_=0.0, to=0.2, resolution=0.01, variable=self.var_timing_randomness, orient=tk.HORIZONTAL, showvalue=False).grid(row=6, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_timing_randomness).grid(row=6, column=2, padx=5, pady=5)
        
        frame.columnconfigure(1, weight=1)

    def create_accessibility_tab(self, notebook):
        """Create accessibility settings and calibration"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Accessibility")

        # Accessibility toggles and sliders
        self.var_access_enabled = tk.BooleanVar(value=True)
        ttk.Checkbutton(frame, text="Enable Accessibility Mode", variable=self.var_access_enabled).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        ttk.Label(frame, text="Sticky Bubble Radius (px):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.var_sticky_bubble = tk.IntVar(value=30)
        ttk.Scale(frame, from_=10, to=150, variable=self.var_sticky_bubble, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_sticky_bubble).grid(row=1, column=2, padx=5, pady=5)

        ttk.Label(frame, text="Dwell-to-Click (ms):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.var_dwell_ms = tk.IntVar(value=400)
        ttk.Scale(frame, from_=200, to=900, variable=self.var_dwell_ms, orient=tk.HORIZONTAL).grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_dwell_ms).grid(row=2, column=2, padx=5, pady=5)

        ttk.Label(frame, text="Tremor Filter Alpha:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.var_tremor_alpha = tk.DoubleVar(value=0.3)
        tk.Scale(frame, from_=0.1, to=0.6, resolution=0.01, variable=self.var_tremor_alpha, orient=tk.HORIZONTAL, showvalue=False).grid(row=3, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(frame, textvariable=self.var_tremor_alpha).grid(row=3, column=2, padx=5, pady=5)

        ttk.Label(frame, text="Aim Area:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.var_aim_area = tk.StringVar(value="head")
        ttk.Combobox(frame, textvariable=self.var_aim_area, values=["head", "neck", "chest"], state='readonly').grid(row=4, column=1, sticky=tk.EW, padx=5, pady=5)

        # Calibration wizard
        ttk.Label(frame, text="Calibration Wizard", font=('TkDefaultFont', 10, 'bold')).grid(row=5, column=0, columnspan=3, sticky=tk.W, padx=5, pady=(15,5))
        ttk.Button(frame, text="Run 60s Calibration", command=self.run_calibration).grid(row=6, column=0, padx=5, pady=5)

        frame.columnconfigure(1, weight=1)

    def run_calibration(self):
        """Simple calibration placeholder: adjusts starting values to mid-comfort"""
        try:
            # Mid-range defaults for comfort
            self.var_sticky_bubble.set(60)
            self.var_dwell_ms.set(500)
            self.var_tremor_alpha.set(0.35)
            messagebox.showinfo("Calibration", "Calibration complete. You can fine-tune with the sliders.")
        except Exception as e:
            messagebox.showerror("Calibration Error", str(e))
    
    def create_sensitivity_tab(self, notebook):
        """Create sensitivity settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Sensitivity")
        
        # Instructions
        instructions = tk.Text(frame, height=4, wrap=tk.WORD)
        instructions.insert(tk.END, "Instructions:\n1. Set your in-game X and Y axis sensitivity to the same value\n2. Set your targeting sensitivity to match your scoping sensitivity\n3. Enter the exact values from your game settings below")
        instructions.config(state=tk.DISABLED)
        instructions.grid(row=0, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=5)
        
        # XY Sensitivity
        ttk.Label(frame, text="X/Y Axis Sensitivity:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.var_xy_sens, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Targeting Sensitivity
        ttk.Label(frame, text="Targeting Sensitivity:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(frame, textvariable=self.var_targeting_sens, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Calculate button
        ttk.Button(frame, text="Calculate Scales", command=self.calculate_sensitivity_scales).grid(row=3, column=0, columnspan=2, pady=10)
        
        # Display calculated values
        self.sensitivity_info = tk.Text(frame, height=6, wrap=tk.WORD)
        self.sensitivity_info.grid(row=4, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=5)
        
        frame.columnconfigure(2, weight=1)
    
    def create_profiles_tab(self, notebook):
        """Create profiles management tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Profiles")
        
        # Profile list
        ttk.Label(frame, text="Available Profiles:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.profile_listbox = tk.Listbox(frame, height=10)
        self.profile_listbox.grid(row=1, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)
        
        # Profile buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="Load Profile", command=self.load_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Save Profile", command=self.save_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Delete Profile", command=self.delete_profile).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Refresh", command=self.refresh_profiles).pack(side=tk.LEFT, padx=5)
        
        # Load profiles
        self.refresh_profiles()
        
        frame.columnconfigure(1, weight=1)
    
    def create_monitoring_tab(self, notebook):
        """Create monitoring tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Monitoring")
        
        # Performance metrics
        self.metrics_text = tk.Text(frame, height=15, wrap=tk.WORD, font=('Courier', 10))
        self.metrics_text.grid(row=0, column=0, sticky=tk.NSEW, padx=5, pady=5)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.metrics_text.yview)
        scrollbar.grid(row=0, column=1, sticky=tk.NS)
        self.metrics_text.config(yscrollcommand=scrollbar.set)
        
        # Refresh button
        ttk.Button(frame, text="Refresh Metrics", command=self.refresh_metrics).grid(row=1, column=0, pady=5)
        
        frame.columnconfigure(0, weight=1)
        frame.rowconfigure(0, weight=1)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = ttk.Label(self.root, textvariable=self.var_status, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_control_buttons(self):
        """Create control buttons"""
        button_frame = ttk.Frame(self.root)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="Apply Settings", command=self.apply_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Reset to Defaults", command=self.reset_to_defaults).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Start Aimbot", command=self.start_aimbot).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Stop Aimbot", command=self.stop_aimbot).pack(side=tk.RIGHT, padx=5)
    
    def load_current_config(self):
        """Load current configuration into GUI"""
        self.calculate_sensitivity_scales()
    
    def calculate_sensitivity_scales(self):
        """Calculate and display sensitivity scales"""
        try:
            xy_sens = self.var_xy_sens.get()
            targeting_sens = self.var_targeting_sens.get()
            
            if xy_sens <= 0 or targeting_sens <= 0:
                raise ValueError("Sensitivity values must be positive")
            
            xy_scale = 10.0 / xy_sens
            targeting_scale = 1000.0 / (targeting_sens * xy_sens)
            
            info_text = f"""Calculated Sensitivity Scales:
XY Scale: {xy_scale:.4f}
Targeting Scale: {targeting_scale:.4f}

These values will be used for mouse movement calculations.
Higher scales = more sensitive movement.
Lower scales = less sensitive movement.
"""
            
            self.sensitivity_info.config(state=tk.NORMAL)
            self.sensitivity_info.delete(1.0, tk.END)
            self.sensitivity_info.insert(tk.END, info_text)
            self.sensitivity_info.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to calculate sensitivity scales: {e}")
    
    def apply_settings(self):
        """Apply current settings"""
        try:
            # Update aimbot config
            self.aimbot_config.fov = self.var_fov.get()
            self.aimbot_config.confidence_threshold = self.var_confidence.get()
            self.aimbot_config.iou_threshold = self.var_iou.get()
            self.aimbot_config.aim_height_ratio = self.var_aim_height.get()
            self.aimbot_config.mouse_method = MouseMethod(self.var_mouse_method.get())
            self.aimbot_config.mouse_delay = self.var_mouse_delay.get()
            self.aimbot_config.use_trigger_bot = self.var_trigger_bot.get()
            self.aimbot_config.max_fps = self.var_max_fps.get()
            self.aimbot_config.enable_randomization = self.var_enable_randomization.get()
            self.aimbot_config.movement_randomness = self.var_movement_randomness.get()
            self.aimbot_config.timing_randomness = self.var_timing_randomness.get()
            # Accessibility
            self.aimbot_config.accessibility_enabled = self.var_access_enabled.get()
            self.aimbot_config.sticky_bubble_radius_px = self.var_sticky_bubble.get()
            self.aimbot_config.dwell_click_ms = self.var_dwell_ms.get()
            self.aimbot_config.tremor_filter_alpha = self.var_tremor_alpha.get()
            self.aimbot_config.aim_area = self.var_aim_area.get()
            
            # Save configurations
            self.config_manager.save_aimbot_config(self.aimbot_config)
            self.config_manager.save_sensitivity_config(
                self.var_xy_sens.get(),
                self.var_targeting_sens.get()
            )
            
            self.var_status.set("Settings applied successfully")
            messagebox.showinfo("Success", "Settings applied and saved successfully!")
            
        except Exception as e:
            self.var_status.set(f"Error: {e}")
            messagebox.showerror("Error", f"Failed to apply settings: {e}")
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        if messagebox.askyesno("Confirm Reset", "Reset all settings to defaults?"):
            default_config = AimbotConfig()
            
            self.var_fov.set(default_config.fov)
            self.var_confidence.set(default_config.confidence_threshold)
            self.var_iou.set(default_config.iou_threshold)
            self.var_aim_height.set(default_config.aim_height_ratio)
            self.var_mouse_method.set(default_config.mouse_method.value)
            self.var_mouse_delay.set(default_config.mouse_delay)
            self.var_trigger_bot.set(default_config.use_trigger_bot)
            self.var_max_fps.set(default_config.max_fps)
            self.var_enable_randomization.set(default_config.enable_randomization)
            self.var_movement_randomness.set(default_config.movement_randomness)
            self.var_timing_randomness.set(default_config.timing_randomness)
            # Accessibility
            self.var_access_enabled.set(default_config.accessibility_enabled)
            self.var_sticky_bubble.set(default_config.sticky_bubble_radius_px)
            self.var_dwell_ms.set(default_config.dwell_click_ms)
            self.var_tremor_alpha.set(default_config.tremor_filter_alpha)
            self.var_aim_area.set(default_config.aim_area)
            
            self.var_xy_sens.set(1.0)
            self.var_targeting_sens.set(1.0)
            
            self.calculate_sensitivity_scales()
            self.var_status.set("Settings reset to defaults")
    
    def refresh_profiles(self):
        """Refresh the profiles list"""
        self.profile_listbox.delete(0, tk.END)
        profiles = self.config_manager.list_profiles()
        for profile in profiles:
            self.profile_listbox.insert(tk.END, profile)
    
    def load_profile(self):
        """Load selected profile"""
        selection = self.profile_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a profile to load")
            return
        
        profile_name = self.profile_listbox.get(selection[0])
        result = self.config_manager.load_profile(profile_name)
        
        if result:
            config, sensitivity = result
            # Update GUI variables
            self.var_fov.set(config.fov)
            self.var_confidence.set(config.confidence_threshold)
            # ... (update all other variables)
            
            self.var_status.set(f"Profile '{profile_name}' loaded")
            messagebox.showinfo("Success", f"Profile '{profile_name}' loaded successfully!")
        else:
            messagebox.showerror("Error", f"Failed to load profile '{profile_name}'")
    
    def save_profile(self):
        """Save current settings as a profile"""
        profile_name = tk.simpledialog.askstring("Save Profile", "Enter profile name:")
        if not profile_name:
            return
        
        try:
            # Apply current settings first
            self.apply_settings()
            
            # Save as profile
            self.config_manager.save_profile(
                profile_name,
                self.aimbot_config,
                self.config_manager.load_sensitivity_config()
            )
            
            self.refresh_profiles()
            self.var_status.set(f"Profile '{profile_name}' saved")
            messagebox.showinfo("Success", f"Profile '{profile_name}' saved successfully!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save profile: {e}")
    
    def delete_profile(self):
        """Delete selected profile"""
        selection = self.profile_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a profile to delete")
            return
        
        profile_name = self.profile_listbox.get(selection[0])
        
        if messagebox.askyesno("Confirm Delete", f"Delete profile '{profile_name}'?"):
            if self.config_manager.delete_profile(profile_name):
                self.refresh_profiles()
                self.var_status.set(f"Profile '{profile_name}' deleted")
                messagebox.showinfo("Success", f"Profile '{profile_name}' deleted successfully!")
            else:
                messagebox.showerror("Error", f"Failed to delete profile '{profile_name}'")
    
    def refresh_metrics(self):
        """Refresh performance metrics display"""
        # This would be connected to the actual aimbot performance data
        metrics_text = """Performance Metrics:
FPS: 60.0
Frame Time: 16.7ms
Detection Time: 5.2ms
Movement Time: 1.1ms
Memory Usage: 245.6MB
CPU Usage: 15.2%
GPU Usage: 45.8%

Status: Running
Targets Detected: 1,234
Shots Fired: 567
Accuracy: 78.5%
"""
        
        self.metrics_text.config(state=tk.NORMAL)
        self.metrics_text.delete(1.0, tk.END)
        self.metrics_text.insert(tk.END, metrics_text)
        self.metrics_text.config(state=tk.DISABLED)
    
    def start_aimbot(self):
        """Start the aimbot"""
        try:
            # Apply latest settings before start
            self.apply_settings()
            if self.aimbot_instance is None:
                self.aimbot_instance = EnhancedAimbot(self.aimbot_config, collect_data=False)
            if self.aimbot_thread is None or not self.aimbot_thread.is_alive():
                self.aimbot_thread = threading.Thread(target=self.aimbot_instance.start, daemon=True)
                self.aimbot_thread.start()
            if self.on_start_callback:
                self.on_start_callback()
            self.var_status.set("Aimbot started")
        except Exception as e:
            self.var_status.set(f"Start error: {e}")
            messagebox.showerror("Start Error", str(e))
    
    def stop_aimbot(self):
        """Stop the aimbot"""
        try:
            if self.aimbot_instance is not None:
                self.aimbot_instance.stop()
            if self.on_stop_callback:
                self.on_stop_callback()
            self.var_status.set("Aimbot stopped")
        except Exception as e:
            self.var_status.set(f"Stop error: {e}")
            messagebox.showerror("Stop Error", str(e))
    
    def run(self):
        """Run the GUI application"""
        self.root.mainloop()


if __name__ == "__main__":
    app = AimbotGUI()
    app.run()
