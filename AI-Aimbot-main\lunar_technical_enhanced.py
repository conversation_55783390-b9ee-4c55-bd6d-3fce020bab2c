#!/usr/bin/env python3
"""
Lunar AI Aimbot - Technical Enhanced Edition
Implements all technical concepts from AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md
Features comprehensive system integration, display mode detection, and advanced input methods
"""

import argparse
import json
import os
import sys
import signal
import threading
import time
from pathlib import Path
from pynput import keyboard
from termcolor import colored

# Add lib directory to path
sys.path.insert(0, str(Path(__file__).parent / "lib"))

from aimbot import EnhancedAimbot, AimbotConfig, MouseMethod, Logger
from config_manager import ConfigManager
from system_integration_manager import SystemIntegrationManager, SystemConfiguration, SystemStatus
from display_mode_detector import DisplayMode
from enhanced_screen_capture import CaptureMethod
from input_method_manager import InputMethod


class TechnicalEnhancedLunarApp:
    """
    Technical Enhanced Lunar Application
    Implements comprehensive system integration from technical documentation
    """
    
    def __init__(self):
        self.logger = Logger("TechnicalLunar")
        self.config_manager = ConfigManager()
        self.system_manager = None
        self.aimbot = None
        self.keyboard_listener = None
        self.running = False
        
        # Technical status
        self.display_mode_warnings_shown = False
        self.last_compatibility_report = ""
        
        self.logger.info("Technical Enhanced Lunar initialized")
    
    def initialize_system_integration(self):
        """Initialize comprehensive system integration"""
        try:
            self.logger.info("Initializing technical system integration...")
            
            # Create system configuration
            system_config = SystemConfiguration(
                auto_detect_display_mode=True,
                preferred_capture_method=CaptureMethod.MSS,
                input_method=InputMethod.WIN32_SENDINPUT,
                enable_humanization=True,
                enable_performance_monitoring=True,
                auto_optimize=True,
                target_fps=60.0,
                compatibility_check_interval=5.0,
                auto_fallback=True
            )
            
            # Initialize system manager
            self.system_manager = SystemIntegrationManager(system_config, self.logger)
            
            # Set up callbacks for system events
            self.system_manager.set_compatibility_change_callback(self._on_compatibility_change)
            
            # Start system monitoring
            self.system_manager.start_monitoring()
            
            self.logger.info("System integration initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"System integration initialization failed: {e}")
            return False
    
    def _on_compatibility_change(self, display_info):
        """Handle compatibility changes"""
        self.logger.info(f"Display mode changed to: {display_info.mode.value}")
        
        # Show warnings for problematic display modes
        if display_info.mode == DisplayMode.FULLSCREEN_EXCLUSIVE and not self.display_mode_warnings_shown:
            self._show_fullscreen_warning()
            self.display_mode_warnings_shown = True
        elif display_info.mode == DisplayMode.BORDERLESS_WINDOWED:
            if self.display_mode_warnings_shown:
                self.logger.info("Display mode is now optimal for aimbot operation")
                self.display_mode_warnings_shown = False
    
    def _show_fullscreen_warning(self):
        """Show fullscreen exclusive mode warning"""
        warning_msg = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                            ⚠️  DISPLAY MODE WARNING ⚠️                           ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  FULLSCREEN EXCLUSIVE MODE DETECTED                                          ║
║                                                                              ║
║  This mode prevents screen capture and will cause the aimbot to fail.       ║
║                                                                              ║
║  SOLUTION:                                                                   ║
║  1. Switch your game to "Borderless Windowed" or "Windowed Fullscreen"      ║
║  2. Look for this setting in your game's graphics/display options           ║
║  3. This provides the same visual experience with aimbot compatibility       ║
║                                                                              ║
║  TECHNICAL EXPLANATION:                                                      ║
║  Fullscreen exclusive mode bypasses Windows Desktop Window Manager (DWM)    ║
║  and blocks screen capture APIs like BitBlt, DXGI, and MSS library.         ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
        print(colored(warning_msg, "yellow"))
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def on_key_release(self, key):
        """Handle keyboard events with technical features"""
        try:
            if key == keyboard.Key.f1:
                # Toggle aimbot
                if self.aimbot:
                    self.aimbot.toggle()
                    status = "ENABLED" if self.aimbot.is_enabled() else "DISABLED"
                    self.logger.info(f"Aimbot {status}")
            
            elif key == keyboard.Key.f2:
                # Quit application
                self.logger.info("F2 pressed - Shutting down")
                self.shutdown()
            
            elif key == keyboard.Key.f3:
                # Show technical status report
                self._show_technical_status()
            
            elif key == keyboard.Key.f4:
                # Show compatibility report
                self._show_compatibility_report()
            
            elif key == keyboard.Key.f5:
                # Force compatibility check
                self._force_compatibility_check()
            
            elif key == keyboard.Key.f6:
                # Cycle input methods
                self._cycle_input_methods()
            
            elif key == keyboard.Key.f7:
                # Cycle capture methods
                self._cycle_capture_methods()
                
        except Exception as e:
            self.logger.error(f"Key handler error: {e}")
    
    def _show_technical_status(self):
        """Show comprehensive technical status"""
        if self.system_manager:
            report = self.system_manager.get_system_status_report()
            print(colored(report, "cyan"))
        else:
            print(colored("System manager not initialized", "red"))
    
    def _show_compatibility_report(self):
        """Show detailed compatibility report"""
        if self.system_manager and self.system_manager.display_detector:
            display_info = self.system_manager.display_detector.detect_foreground_display_mode()
            report = self.system_manager.display_detector.get_capture_compatibility_report(display_info)
            print(colored(report, "green"))
        else:
            print(colored("Display detector not available", "red"))
    
    def _force_compatibility_check(self):
        """Force immediate compatibility check"""
        if self.system_manager:
            self.logger.info("Forcing compatibility check...")
            self.system_manager._perform_compatibility_check()
            print(colored("Compatibility check completed", "green"))
        else:
            print(colored("System manager not available", "red"))
    
    def _cycle_input_methods(self):
        """Cycle through available input methods"""
        if self.system_manager and self.system_manager.input_manager:
            input_manager = self.system_manager.input_manager
            available_methods = list(input_manager.input_methods.keys())
            
            if available_methods:
                current_index = available_methods.index(input_manager.current_method)
                next_index = (current_index + 1) % len(available_methods)
                next_method = available_methods[next_index]
                
                if input_manager.switch_input_method(next_method):
                    print(colored(f"Switched to input method: {next_method.value}", "green"))
                else:
                    print(colored("Failed to switch input method", "red"))
        else:
            print(colored("Input manager not available", "red"))
    
    def _cycle_capture_methods(self):
        """Cycle through available capture methods"""
        if self.system_manager and self.system_manager.screen_capture:
            screen_capture = self.system_manager.screen_capture
            available_methods = list(screen_capture.capture_methods.keys())
            
            if available_methods:
                current_index = available_methods.index(screen_capture.current_method)
                next_index = (current_index + 1) % len(available_methods)
                next_method = available_methods[next_index]
                
                screen_capture.current_method = next_method
                print(colored(f"Switched to capture method: {next_method.value}", "green"))
        else:
            print(colored("Screen capture not available", "red"))
    
    def show_banner(self):
        """Show technical enhanced banner"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🧠 LUNAR AI AIMBOT - TECHNICAL ENHANCED 🧠                  ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  TECHNICAL FEATURES:                                                         ║
║  • Advanced Display Mode Detection                                           ║
║  • Multiple Screen Capture Methods with Fallbacks                           ║
║  • Humanized Input Simulation                                                ║
║  • Real-time Performance Monitoring                                          ║
║  • Automatic System Optimization                                             ║
║  • Comprehensive Compatibility Analysis                                      ║
║                                                                              ║
║  INTEGRATION METHOD: Computer Vision/Screen Capture                          ║
║  DETECTION RISK: Medium (Lower than memory-based methods)                   ║
║  COMPATIBILITY: Universal (Works with any game)                              ║
║  WINDOWED MODE: Required for fullscreen games                               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
        print(colored(banner, "cyan"))
    
    def show_controls(self):
        """Show enhanced control information"""
        controls = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                              🎮 CONTROLS 🎮                                   ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  F1  - Toggle Aimbot On/Off                                                  ║
║  F2  - Quit Application                                                      ║
║  F3  - Show Technical Status Report                                          ║
║  F4  - Show Compatibility Report                                             ║
║  F5  - Force Compatibility Check                                             ║
║  F6  - Cycle Input Methods                                                   ║
║  F7  - Cycle Screen Capture Methods                                          ║
║                                                                              ║
║  Right Mouse Button - Aim (hold to activate aimbot)                         ║
║  Left Mouse Button  - Shoot (automatic with trigger bot)                    ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
        print(colored(controls, "green"))
    
    def run(self, args):
        """Main application entry point"""
        try:
            self.show_banner()
            
            # Initialize system integration
            if not self.initialize_system_integration():
                self.logger.error("Failed to initialize system integration")
                return False
            
            # Setup signal handlers
            self.setup_signal_handlers()
            
            # Initialize aimbot with system integration
            config = self.config_manager.load_config()
            aimbot_config = AimbotConfig(**config)
            
            # Use integrated screen capture and input methods
            self.aimbot = EnhancedAimbot(aimbot_config)
            
            # Replace aimbot's screen capture with integrated version
            if self.system_manager and self.system_manager.screen_capture:
                self.aimbot.screen_capture = self.system_manager.screen_capture
                self.logger.info("Integrated enhanced screen capture with aimbot")
            
            # Start keyboard listener
            self.keyboard_listener = keyboard.Listener(on_release=self.on_key_release)
            self.keyboard_listener.start()
            
            self.show_controls()
            
            # Show initial compatibility status
            time.sleep(1)  # Allow system to initialize
            self._show_compatibility_report()
            
            self.running = True
            self.logger.info("Technical Enhanced Lunar started successfully")
            
            # Main loop
            try:
                self.aimbot.run()
            except KeyboardInterrupt:
                self.logger.info("Keyboard interrupt received")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            return False
        finally:
            self.shutdown()
    
    def shutdown(self):
        """Graceful shutdown"""
        if not self.running:
            return
        
        self.running = False
        self.logger.info("Shutting down Technical Enhanced Lunar...")
        
        # Stop aimbot
        if self.aimbot:
            self.aimbot.stop()
        
        # Stop keyboard listener
        if self.keyboard_listener:
            self.keyboard_listener.stop()
        
        # Shutdown system integration
        if self.system_manager:
            self.system_manager.shutdown()
        
        self.logger.info("Shutdown complete")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Lunar AI Aimbot - Technical Enhanced Edition")
    parser.add_argument("--setup", action="store_true", help="Run configuration setup")
    parser.add_argument("--compatibility-check", action="store_true", help="Run compatibility check only")
    parser.add_argument("--list-methods", action="store_true", help="List available capture and input methods")
    
    args = parser.parse_args()
    
    # Hide pygame support prompt
    os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'
    
    app = TechnicalEnhancedLunarApp()
    
    if args.compatibility_check:
        # Run compatibility check only
        if app.initialize_system_integration():
            app._show_compatibility_report()
        return 0
    
    if args.list_methods:
        # List available methods
        if app.initialize_system_integration():
            print("Available Screen Capture Methods:")
            for method in app.system_manager.screen_capture.capture_methods.keys():
                print(f"  - {method.value}")
            
            print("\nAvailable Input Methods:")
            for method in app.system_manager.input_manager.input_methods.keys():
                print(f"  - {method.value}")
        return 0
    
    success = app.run(args)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
