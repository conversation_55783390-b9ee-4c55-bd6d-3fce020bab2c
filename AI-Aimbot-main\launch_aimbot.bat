@echo off
title 🎯 ULTIMATE AI AIMBOT LAUNCHER
color 0A

echo.
echo ========================================
echo    🎯 ULTIMATE AI AIMBOT LAUNCHER
echo ========================================
echo.
echo [1] Launch GUI Interface (Recommended)
echo [2] Launch Lightning Fast Learner
echo [3] Launch CPU Optimized Version
echo [4] Launch Ultimate Speed Hybrid
echo [5] Install/Update Dependencies
echo [6] Exit
echo.
set /p choice="Select option (1-6): "

if "%choice%"=="1" goto gui
if "%choice%"=="2" goto lightning
if "%choice%"=="3" goto cpu
if "%choice%"=="4" goto hybrid
if "%choice%"=="5" goto install
if "%choice%"=="6" goto exit

:gui
echo.
echo 🚀 Launching GUI Interface...
echo.
call .venv\Scripts\activate.bat
python AI-Aimbot-main\aimbot_gui.py
pause
goto menu

:lightning
echo.
echo ⚡ Launching Lightning Fast Learner...
echo.
call .venv\Scripts\activate.bat
python AI-Aimbot-main\lightning_fast_learner.py
pause
goto menu

:cpu
echo.
echo 💻 Launching CPU Optimized Version...
echo.
call .venv\Scripts\activate.bat
python AI-Aimbot-main\cpu_optimized_aimbot.py
pause
goto menu

:hybrid
echo.
echo 🚀 Launching Ultimate Speed Hybrid...
echo.
call .venv\Scripts\activate.bat
python AI-Aimbot-main\ultimate_speed_hybrid.py
pause
goto menu

:install
echo.
echo 📦 Installing/Updating Dependencies...
echo.
call .venv\Scripts\activate.bat
pip install --upgrade torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install --upgrade opencv-python numpy mss pillow pywin32 psutil numba
echo.
echo ✅ Dependencies updated!
pause
goto menu

:exit
echo.
echo 👋 Thanks for using Ultimate AI Aimbot!
echo.
exit

:menu
cls
goto start
