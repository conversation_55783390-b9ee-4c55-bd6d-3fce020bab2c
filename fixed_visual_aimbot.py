#!/usr/bin/env python3
"""
🎯 FIXED VISUAL AIMBOT
=====================
Fixed version that shows visual feedback without window spawning issues:
• Single stable overlay window
• Real-time target detection
• No interference with game rendering
• Proper window management
• Clean shutdown
"""

import cv2
import numpy as np
import mss
import time
import threading
import win32api
import win32gui
import win32con
import signal
import sys
from collections import deque

class FixedVisualAimbot:
    def __init__(self):
        self.running = False
        self.window_created = False
        
        # Performance tracking
        self.fps = 0
        self.frame_times = deque(maxlen=30)
        self.detections = 0
        self.movements = 0
        
        # Settings
        self.fov_size = 400
        self.sensitivity = 0.7
        self.confidence_threshold = 0.6
        self.smoothing = 0.8
        
        # Visual settings
        self.show_fov = True
        self.show_targets = True
        self.show_crosshair = True
        self.show_stats = True
        
        # Colors (BGR format)
        self.colors = {
            'fov': (0, 255, 255),      # Yellow FOV circle
            'target': (0, 0, 255),     # Red target boxes
            'crosshair': (0, 255, 0),  # Green crosshair
            'text': (255, 255, 255),   # White text
            'bg': (0, 0, 0)            # Black background
        }
        
        # Screen info
        self.screen_w = win32api.GetSystemMetrics(0)
        self.screen_h = win32api.GetSystemMetrics(1)
        
        # Screen capture (initialize in thread)
        self.sct = None
        
        # Setup clean shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        
        print("🎯 Fixed Visual Aimbot initialized")
        print(f"📺 Screen: {self.screen_w}x{self.screen_h}")
        
    def signal_handler(self, signum, frame):
        """Handle Ctrl+C gracefully"""
        print("\n🛑 Shutdown signal received...")
        self.stop()
        
    def detect_targets(self, frame):
        """Enhanced target detection"""
        targets = []
        
        try:
            # Convert to HSV for better color detection
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            
            # Enemy color ranges (optimized)
            color_ranges = [
                # Red enemies
                ([0, 100, 100], [10, 255, 255]),
                ([170, 100, 100], [180, 255, 255]),
                # Blue enemies
                ([100, 100, 100], [130, 255, 255]),
                # Yellow/Orange enemies
                ([15, 100, 100], [35, 255, 255])
            ]
            
            for i, (lower, upper) in enumerate(color_ranges):
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                
                # Clean up the mask
                kernel = np.ones((3,3), np.uint8)
                mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
                mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
                
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if 100 < area < 4000:  # Filter by size
                        x, y, w, h = cv2.boundingRect(contour)
                        
                        # Calculate confidence
                        aspect_ratio = w / h if h > 0 else 0
                        confidence = min((area / 1500.0) * (1.0 if 0.5 < aspect_ratio < 2.0 else 0.7), 1.0)
                        
                        if confidence > self.confidence_threshold:
                            targets.append({
                                'x': x + w // 2,
                                'y': y + h // 2,
                                'w': w,
                                'h': h,
                                'confidence': confidence,
                                'color_type': i
                            })
            
            # Remove duplicate targets
            filtered_targets = []
            for target in targets:
                is_duplicate = False
                for existing in filtered_targets:
                    distance = np.sqrt((target['x'] - existing['x'])**2 + (target['y'] - existing['y'])**2)
                    if distance < 30:
                        if target['confidence'] > existing['confidence']:
                            filtered_targets.remove(existing)
                        else:
                            is_duplicate = True
                        break
                
                if not is_duplicate:
                    filtered_targets.append(target)
            
            return filtered_targets
            
        except Exception as e:
            print(f"⚠️ Detection error: {e}")
            return []
    
    def draw_overlay(self, frame, targets):
        """Draw visual overlay on frame"""
        h, w = frame.shape[:2]
        center_x, center_y = w // 2, h // 2
        
        # Draw FOV circle
        if self.show_fov:
            radius = min(w, h) // 2 - 10
            cv2.circle(frame, (center_x, center_y), radius, self.colors['fov'], 2)
        
        # Draw crosshair
        if self.show_crosshair:
            size = 20
            cv2.line(frame, (center_x - size, center_y), (center_x + size, center_y), self.colors['crosshair'], 2)
            cv2.line(frame, (center_x, center_y - size), (center_x, center_y + size), self.colors['crosshair'], 2)
            cv2.circle(frame, (center_x, center_y), 3, self.colors['crosshair'], -1)
        
        # Draw targets
        if self.show_targets and targets:
            for i, target in enumerate(targets):
                x, y = target['x'], target['y']
                w_box, h_box = target['w'], target['h']
                confidence = target['confidence']
                
                # Target box
                cv2.rectangle(frame, (x - w_box//2, y - h_box//2), 
                             (x + w_box//2, y + h_box//2), self.colors['target'], 2)
                
                # Target center
                cv2.circle(frame, (x, y), 4, self.colors['target'], -1)
                
                # Confidence text
                cv2.putText(frame, f"{confidence:.2f}", (x - w_box//2, y - h_box//2 - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, self.colors['target'], 1)
                
                # Distance from center
                distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                cv2.putText(frame, f"{distance:.0f}px", (x + 10, y + 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, self.colors['text'], 1)
                
                # Line to target
                cv2.line(frame, (center_x, center_y), (x, y), self.colors['target'], 1)
        
        # Draw stats
        if self.show_stats:
            stats = [
                f"FPS: {self.fps:.0f}",
                f"Targets: {len(targets)}",
                f"Detections: {self.detections}",
                f"Movements: {self.movements}",
                f"FOV: {self.fov_size}px"
            ]
            
            for i, stat in enumerate(stats):
                y_pos = 30 + i * 25
                cv2.putText(frame, stat, (10, y_pos), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, self.colors['text'], 2)
        
        # Status indicator
        status = "🎯 ACTIVE" if self.running else "⚫ STOPPED"
        cv2.putText(frame, status, (10, h - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
        
        return frame
    
    def move_to_target(self, target, region):
        """Move mouse to target with smoothing"""
        try:
            # Calculate screen coordinates
            target_screen_x = region['left'] + target['x']
            target_screen_y = region['top'] + target['y']
            
            # Get current mouse position
            current_x, current_y = win32gui.GetCursorPos()
            
            # Apply smoothing and sensitivity
            dx = (target_screen_x - current_x) * self.sensitivity * self.smoothing
            dy = (target_screen_y - current_y) * self.sensitivity * self.smoothing
            
            new_x = int(current_x + dx)
            new_y = int(current_y + dy)
            
            # Move mouse
            win32api.SetCursorPos((new_x, new_y))
            self.movements += 1
            
            return True
            
        except Exception as e:
            print(f"⚠️ Movement error: {e}")
            return False
    
    def run(self):
        """Main aimbot loop"""
        self.running = True
        
        print("🚀 Starting Fixed Visual Aimbot...")
        print("🎮 Look for the overlay window!")
        print("🎯 Controls: Q=Quit, T=Toggle targets, F=Toggle FOV, C=Toggle crosshair")
        print("-" * 60)
        
        # Initialize screen capture in this thread
        self.sct = mss.mss()
        
        frame_count = 0
        
        try:
            while self.running:
                frame_start = time.perf_counter()
                
                # Calculate capture region
                center_x = self.screen_w // 2
                center_y = self.screen_h // 2
                
                region = {
                    'left': center_x - self.fov_size // 2,
                    'top': center_y - self.fov_size // 2,
                    'width': self.fov_size,
                    'height': self.fov_size
                }
                
                # Capture screenshot
                screenshot = self.sct.grab(region)
                frame = np.array(screenshot)
                frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
                
                # Detect targets
                targets = self.detect_targets(frame)
                
                if targets:
                    self.detections += len(targets)
                    
                    # Move to best target
                    best_target = max(targets, key=lambda t: t['confidence'])
                    if best_target['confidence'] > 0.8:
                        self.move_to_target(best_target, region)
                
                # Draw overlay
                overlay_frame = self.draw_overlay(frame, targets)
                
                # Show window (create only once)
                if not self.window_created:
                    cv2.namedWindow('🎯 AIMBOT OVERLAY', cv2.WINDOW_NORMAL)
                    cv2.resizeWindow('🎯 AIMBOT OVERLAY', 600, 600)
                    self.window_created = True
                
                cv2.imshow('🎯 AIMBOT OVERLAY', overlay_frame)
                
                # Handle key presses
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q') or key == ord('Q'):
                    break
                elif key == ord('t') or key == ord('T'):
                    self.show_targets = not self.show_targets
                    print(f"Target display: {'ON' if self.show_targets else 'OFF'}")
                elif key == ord('f') or key == ord('F'):
                    self.show_fov = not self.show_fov
                    print(f"FOV display: {'ON' if self.show_fov else 'OFF'}")
                elif key == ord('c') or key == ord('C'):
                    self.show_crosshair = not self.show_crosshair
                    print(f"Crosshair: {'ON' if self.show_crosshair else 'OFF'}")
                
                # Update FPS
                frame_time = time.perf_counter() - frame_start
                self.frame_times.append(frame_time)
                
                frame_count += 1
                if frame_count % 15 == 0:
                    avg_frame_time = sum(self.frame_times) / len(self.frame_times)
                    self.fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
                
                # Small delay
                time.sleep(0.001)
                
        except Exception as e:
            print(f"❌ Error in main loop: {e}")
        finally:
            self.cleanup()
    
    def stop(self):
        """Stop the aimbot"""
        self.running = False
        
    def cleanup(self):
        """Clean shutdown"""
        self.running = False
        
        # Close screen capture
        if self.sct:
            try:
                self.sct.close()
            except:
                pass
        
        # Close OpenCV windows
        try:
            cv2.destroyAllWindows()
        except:
            pass
        
        print(f"\n🛑 Fixed Visual Aimbot stopped")
        print(f"📊 Final Stats:")
        print(f"   • Total detections: {self.detections}")
        print(f"   • Mouse movements: {self.movements}")
        print(f"   • Final FPS: {self.fps:.0f}")
        print("👋 Clean shutdown complete")

def main():
    print("🎯 FIXED VISUAL AIMBOT")
    print("=" * 30)
    print("Fixed issues:")
    print("• Single stable overlay window")
    print("• No window spawning loops")
    print("• Proper resource cleanup")
    print("• Clean shutdown with Ctrl+C")
    print("• Real-time visual feedback")
    print()
    
    try:
        aimbot = FixedVisualAimbot()
        aimbot.run()
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Failed to start: {e}")
    finally:
        print("Exiting...")
        sys.exit(0)

if __name__ == "__main__":
    main()
