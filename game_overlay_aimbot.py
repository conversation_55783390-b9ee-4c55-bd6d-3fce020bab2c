#!/usr/bin/env python3
"""
🎯 GAME OVERLAY AIMBOT
=====================
Draws directly on your game screen with transparent overlay
Shows real-time target detection and aimbot status
"""

import cv2
import numpy as np
import mss
import time
import win32api
import win32gui
import win32con
import win32ui
from ctypes import windll
import threading

class GameOverlayAimbot:
    def __init__(self):
        self.running = False
        self.fps = 0
        self.targets_found = 0
        self.shots_fired = 0
        
        # Screen info
        self.screen_w = win32api.GetSystemMetrics(0)
        self.screen_h = win32api.GetSystemMetrics(1)
        
        # FOV settings
        self.fov_size = 300
        self.sensitivity = 0.7
        self.confidence_threshold = 0.5
        
        # Screen capture
        self.sct = mss.mss()
        
        # Get desktop DC for drawing
        self.desktop_dc = win32gui.GetDC(0)
        
        print("🎯 Game Overlay Aimbot initialized")
        print(f"📺 Screen: {self.screen_w}x{self.screen_h}")
        
    def draw_on_screen(self, x, y, text, color=(0, 255, 0)):
        """Draw text directly on the screen"""
        try:
            # Create a device context
            dc = win32gui.GetDC(0)
            
            # Set text color (RGB)
            win32gui.SetTextColor(dc, win32api.RGB(color[2], color[1], color[0]))
            win32gui.SetBkMode(dc, win32con.TRANSPARENT)
            
            # Draw text
            win32gui.TextOut(dc, x, y, text)
            
            # Release DC
            win32gui.ReleaseDC(0, dc)
        except:
            pass
    
    def draw_crosshair_on_screen(self, x, y, size=20, color=(0, 255, 0)):
        """Draw crosshair directly on screen"""
        try:
            dc = win32gui.GetDC(0)
            
            # Create pen
            pen = win32gui.CreatePen(win32con.PS_SOLID, 2, win32api.RGB(color[2], color[1], color[0]))
            old_pen = win32gui.SelectObject(dc, pen)
            
            # Draw crosshair lines
            win32gui.MoveToEx(dc, x - size, y)
            win32gui.LineTo(dc, x + size, y)
            win32gui.MoveToEx(dc, x, y - size)
            win32gui.LineTo(dc, x, y + size)
            
            # Cleanup
            win32gui.SelectObject(dc, old_pen)
            win32gui.DeleteObject(pen)
            win32gui.ReleaseDC(0, dc)
        except:
            pass
    
    def draw_target_box_on_screen(self, x, y, w, h, confidence, color=(255, 0, 0)):
        """Draw target box directly on screen"""
        try:
            dc = win32gui.GetDC(0)
            
            # Create pen for box
            pen = win32gui.CreatePen(win32con.PS_SOLID, 2, win32api.RGB(color[2], color[1], color[0]))
            old_pen = win32gui.SelectObject(dc, pen)
            
            # Draw rectangle
            win32gui.Rectangle(dc, x - w//2, y - h//2, x + w//2, y + h//2)
            
            # Draw confidence text
            win32gui.SetTextColor(dc, win32api.RGB(color[2], color[1], color[0]))
            win32gui.SetBkMode(dc, win32con.TRANSPARENT)
            win32gui.TextOut(dc, x - w//2, y - h//2 - 20, f"{confidence:.2f}")
            
            # Cleanup
            win32gui.SelectObject(dc, old_pen)
            win32gui.DeleteObject(pen)
            win32gui.ReleaseDC(0, dc)
        except:
            pass
    
    def clear_screen_area(self, x, y, w, h):
        """Clear a screen area by invalidating it"""
        try:
            rect = win32gui.RECT()
            rect.left = x
            rect.top = y
            rect.right = x + w
            rect.bottom = y + h
            win32gui.InvalidateRect(0, rect, True)
        except:
            pass
    
    def detect_targets(self, frame):
        """Detect targets in the frame"""
        targets = []
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Enemy color ranges (adjust these for your game)
        color_ranges = [
            # Red enemies
            ([0, 100, 100], [10, 255, 255]),
            ([170, 100, 100], [180, 255, 255]),
            # Blue enemies
            ([100, 100, 100], [130, 255, 255]),
            # Yellow/Orange
            ([15, 100, 100], [35, 255, 255])
        ]
        
        for lower, upper in color_ranges:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            
            # Clean up the mask
            kernel = np.ones((3,3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 100 < area < 3000:  # Filter by size
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Calculate confidence
                    confidence = min(area / 1500.0, 1.0)
                    
                    if confidence > self.confidence_threshold:
                        targets.append({
                            'x': x + w // 2,
                            'y': y + h // 2,
                            'w': w,
                            'h': h,
                            'confidence': confidence
                        })
        
        return targets
    
    def show_notification(self, message, duration=2):
        """Show notification on screen"""
        def show_and_hide():
            # Show notification
            self.draw_on_screen(50, 50, message, (0, 255, 255))  # Yellow text
            time.sleep(duration)
            # Clear notification area
            self.clear_screen_area(50, 50, 400, 30)
        
        threading.Thread(target=show_and_hide, daemon=True).start()
    
    def run(self):
        """Main aimbot loop"""
        self.running = True
        
        print("🚀 Starting Game Overlay Aimbot...")
        print("🎮 Look at your game screen for visual feedback!")
        print("🎯 Press Ctrl+C to stop")
        
        self.show_notification("🎯 AIMBOT ACTIVATED!", 3)
        
        frame_count = 0
        start_time = time.time()
        last_stats_update = time.time()
        
        try:
            while self.running:
                loop_start = time.perf_counter()
                
                # Get screen center region
                center_x = self.screen_w // 2
                center_y = self.screen_h // 2
                
                region = {
                    'left': center_x - self.fov_size // 2,
                    'top': center_y - self.fov_size // 2,
                    'width': self.fov_size,
                    'height': self.fov_size
                }
                
                # Capture screenshot
                screenshot = self.sct.grab(region)
                frame = np.array(screenshot)
                frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
                
                # Detect targets
                targets = self.detect_targets(frame)
                
                # Clear previous overlays in the region
                self.clear_screen_area(region['left'], region['top'], region['width'], region['height'])
                
                # Draw crosshair at screen center
                self.draw_crosshair_on_screen(center_x, center_y, 15, (0, 255, 0))
                
                # Process targets
                if targets:
                    self.targets_found += len(targets)
                    
                    # Draw target boxes
                    for target in targets:
                        screen_x = region['left'] + target['x']
                        screen_y = region['top'] + target['y']
                        
                        self.draw_target_box_on_screen(
                            screen_x, screen_y, 
                            target['w'], target['h'], 
                            target['confidence'],
                            (255, 0, 0)  # Red boxes
                        )
                    
                    # Move to best target
                    best_target = max(targets, key=lambda t: t['confidence'])
                    target_screen_x = region['left'] + best_target['x']
                    target_screen_y = region['top'] + best_target['y']
                    
                    # Get current mouse position
                    current_x, current_y = win32gui.GetCursorPos()
                    
                    # Apply smoothing
                    dx = (target_screen_x - current_x) * self.sensitivity
                    dy = (target_screen_y - current_y) * self.sensitivity
                    
                    new_x = int(current_x + dx)
                    new_y = int(current_y + dy)
                    
                    # Move mouse
                    win32api.SetCursorPos((new_x, new_y))
                    
                    # Show target acquired notification
                    if best_target['confidence'] > 0.8:
                        self.show_notification(f"🎯 TARGET LOCKED: {best_target['confidence']:.2f}", 0.5)
                
                # Update FPS
                frame_count += 1
                if frame_count % 30 == 0:
                    elapsed = time.time() - start_time
                    self.fps = frame_count / elapsed
                
                # Update stats display every second
                if time.time() - last_stats_update > 1.0:
                    stats_text = f"FPS: {self.fps:.0f} | Targets: {len(targets)} | Found: {self.targets_found}"
                    self.draw_on_screen(10, 10, stats_text, (255, 255, 0))  # Yellow stats
                    last_stats_update = time.time()
                
                # Small delay
                time.sleep(0.001)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping aimbot...")
            self.show_notification("🛑 AIMBOT STOPPED", 2)
        except Exception as e:
            print(f"❌ Error: {e}")
            self.show_notification(f"❌ ERROR: {str(e)}", 3)
        finally:
            self.running = False
            # Clear all overlays
            win32gui.InvalidateRect(0, None, True)
            print("👋 Game Overlay Aimbot stopped")

def main():
    print("🎯 GAME OVERLAY AIMBOT")
    print("=" * 30)
    print("This draws directly on your game screen!")
    print("• Green crosshair at center")
    print("• Red boxes around targets")
    print("• Yellow stats in top-left")
    print("• Real-time notifications")
    print()
    print("🚀 Starting in 3 seconds...")
    
    for i in range(3, 0, -1):
        print(f"⏰ {i}...")
        time.sleep(1)
    
    try:
        aimbot = GameOverlayAimbot()
        aimbot.run()
    except Exception as e:
        print(f"❌ Failed to start: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
