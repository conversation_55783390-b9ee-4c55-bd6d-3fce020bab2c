"""
Enhanced Screen Capture System
Implements technical concepts from AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md
Provides multiple capture methods with automatic fallbacks and optimization
"""

import ctypes
import ctypes.wintypes
import time
import numpy as np
import cv2
from dataclasses import dataclass
from enum import Enum
from typing import Optional, Tuple, List
import mss
import win32gui
import win32ui
import win32con
import win32api
from PIL import Image


class CaptureMethod(Enum):
    """Available screen capture methods"""
    MSS = "mss"  # Multi-Screen Shot (fastest)
    WIN32_BITBLT = "win32_bitblt"  # Windows BitBlt API
    WIN32_PRINTWINDOW = "win32_printwindow"  # PrintWindow API
    PIL_IMAGEGRAB = "pil_imagegrab"  # PIL ImageGrab (fallback)


@dataclass
class CapturePerformance:
    """Capture performance metrics"""
    method: CaptureMethod
    avg_capture_time: float = 0.0
    success_rate: float = 0.0
    frames_captured: int = 0
    frames_failed: int = 0
    last_error: str = ""


class EnhancedScreenCapture:
    """
    Enhanced screen capture system with multiple methods and fallbacks
    Implements concepts from AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md
    """
    
    def __init__(self, logger):
        self.logger = logger
        self.screen_width = ctypes.windll.user32.GetSystemMetrics(0)
        self.screen_height = ctypes.windll.user32.GetSystemMetrics(1)
        
        # Initialize capture methods
        self.capture_methods = {}
        self.performance_metrics = {}
        self.current_method = CaptureMethod.MSS
        self.fallback_order = [
            CaptureMethod.MSS,
            CaptureMethod.WIN32_BITBLT,
            CaptureMethod.WIN32_PRINTWINDOW,
            CaptureMethod.PIL_IMAGEGRAB
        ]
        
        self._initialize_capture_methods()
        self.logger.info(f"Enhanced screen capture initialized with {len(self.capture_methods)} methods")
    
    def _initialize_capture_methods(self):
        """Initialize all available capture methods"""
        
        # MSS (Multi-Screen Shot) - Fastest method
        try:
            mss_instance = mss.mss()
            self.capture_methods[CaptureMethod.MSS] = mss_instance
            self.performance_metrics[CaptureMethod.MSS] = CapturePerformance(CaptureMethod.MSS)
            self.logger.info("MSS capture method initialized")
        except Exception as e:
            self.logger.warning(f"MSS initialization failed: {e}")
        
        # Win32 BitBlt - Direct Windows API
        try:
            self.capture_methods[CaptureMethod.WIN32_BITBLT] = self._init_win32_bitblt()
            self.performance_metrics[CaptureMethod.WIN32_BITBLT] = CapturePerformance(CaptureMethod.WIN32_BITBLT)
            self.logger.info("Win32 BitBlt capture method initialized")
        except Exception as e:
            self.logger.warning(f"Win32 BitBlt initialization failed: {e}")
        
        # Win32 PrintWindow - Window-specific capture
        try:
            self.capture_methods[CaptureMethod.WIN32_PRINTWINDOW] = self._init_win32_printwindow()
            self.performance_metrics[CaptureMethod.WIN32_PRINTWINDOW] = CapturePerformance(CaptureMethod.WIN32_PRINTWINDOW)
            self.logger.info("Win32 PrintWindow capture method initialized")
        except Exception as e:
            self.logger.warning(f"Win32 PrintWindow initialization failed: {e}")
        
        # PIL ImageGrab - Fallback method
        try:
            import PIL.ImageGrab
            self.capture_methods[CaptureMethod.PIL_IMAGEGRAB] = PIL.ImageGrab
            self.performance_metrics[CaptureMethod.PIL_IMAGEGRAB] = CapturePerformance(CaptureMethod.PIL_IMAGEGRAB)
            self.logger.info("PIL ImageGrab capture method initialized")
        except Exception as e:
            self.logger.warning(f"PIL ImageGrab initialization failed: {e}")
    
    def _init_win32_bitblt(self):
        """Initialize Win32 BitBlt capture"""
        return {
            'screen_dc': win32gui.GetDC(0),
            'mem_dc': None,
            'bitmap': None
        }
    
    def _init_win32_printwindow(self):
        """Initialize Win32 PrintWindow capture"""
        return {
            'initialized': True
        }
    
    def capture_screen_region(self, x: int, y: int, width: int, height: int, 
                            method: Optional[CaptureMethod] = None) -> Optional[np.ndarray]:
        """
        Capture screen region using specified or optimal method
        Implements automatic fallback as described in technical documentation
        """
        
        # Use current method if none specified
        if method is None:
            method = self.current_method
        
        # Try primary method
        frame = self._capture_with_method(method, x, y, width, height)
        if frame is not None:
            self._update_performance_metrics(method, True, 0.0)
            return frame
        
        # Try fallback methods
        self.logger.warning(f"Primary capture method {method.value} failed, trying fallbacks")
        for fallback_method in self.fallback_order:
            if fallback_method == method:
                continue  # Skip the method that just failed
            
            if fallback_method not in self.capture_methods:
                continue  # Skip unavailable methods
            
            frame = self._capture_with_method(fallback_method, x, y, width, height)
            if frame is not None:
                self.logger.info(f"Fallback to {fallback_method.value} successful")
                self.current_method = fallback_method  # Switch to working method
                self._update_performance_metrics(fallback_method, True, 0.0)
                return frame
        
        self.logger.error("All capture methods failed")
        return None
    
    def _capture_with_method(self, method: CaptureMethod, x: int, y: int, 
                           width: int, height: int) -> Optional[np.ndarray]:
        """Capture using specific method"""
        
        start_time = time.perf_counter()
        
        try:
            if method == CaptureMethod.MSS:
                return self._capture_mss(x, y, width, height)
            elif method == CaptureMethod.WIN32_BITBLT:
                return self._capture_win32_bitblt(x, y, width, height)
            elif method == CaptureMethod.WIN32_PRINTWINDOW:
                return self._capture_win32_printwindow(x, y, width, height)
            elif method == CaptureMethod.PIL_IMAGEGRAB:
                return self._capture_pil_imagegrab(x, y, width, height)
            else:
                self.logger.error(f"Unknown capture method: {method}")
                return None
                
        except Exception as e:
            capture_time = time.perf_counter() - start_time
            self._update_performance_metrics(method, False, capture_time, str(e))
            self.logger.error(f"Capture failed with {method.value}: {e}")
            return None
    
    def _capture_mss(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """Capture using MSS library"""
        mss_instance = self.capture_methods[CaptureMethod.MSS]
        
        monitor = {
            'left': x,
            'top': y,
            'width': width,
            'height': height
        }
        
        frame = mss_instance.grab(monitor)
        frame_array = np.array(frame, dtype=np.uint8)
        
        if frame_array.size == 0:
            return None
        
        return cv2.cvtColor(frame_array, cv2.COLOR_BGRA2BGR)
    
    def _capture_win32_bitblt(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """Capture using Win32 BitBlt API"""
        bitblt_data = self.capture_methods[CaptureMethod.WIN32_BITBLT]
        
        # Get screen DC
        screen_dc = bitblt_data['screen_dc']
        
        # Create memory DC and bitmap
        mem_dc = win32ui.CreateDCFromHandle(screen_dc).CreateCompatibleDC()
        bitmap = win32ui.CreateBitmap()
        bitmap.CreateCompatibleBitmap(win32ui.CreateDCFromHandle(screen_dc), width, height)
        
        # Select bitmap into memory DC
        mem_dc.SelectObject(bitmap)
        
        # Copy screen to memory DC
        mem_dc.BitBlt((0, 0), (width, height), win32ui.CreateDCFromHandle(screen_dc), (x, y), win32con.SRCCOPY)
        
        # Get bitmap data
        bitmap_info = bitmap.GetInfo()
        bitmap_bits = bitmap.GetBitmapBits(True)
        
        # Convert to numpy array
        frame = np.frombuffer(bitmap_bits, dtype=np.uint8)
        frame = frame.reshape((height, width, 4))  # BGRA format
        frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
        
        # Cleanup
        mem_dc.DeleteDC()
        win32gui.DeleteObject(bitmap.GetHandle())
        
        return frame
    
    def _capture_win32_printwindow(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """Capture using Win32 PrintWindow API"""
        # Get foreground window
        hwnd = win32gui.GetForegroundWindow()
        if not hwnd:
            return None
        
        # Get window DC
        window_dc = win32gui.GetWindowDC(hwnd)
        
        # Create memory DC and bitmap
        mem_dc = win32ui.CreateDCFromHandle(window_dc).CreateCompatibleDC()
        bitmap = win32ui.CreateBitmap()
        bitmap.CreateCompatibleBitmap(win32ui.CreateDCFromHandle(window_dc), width, height)
        
        # Select bitmap into memory DC
        mem_dc.SelectObject(bitmap)
        
        # Print window to memory DC
        result = ctypes.windll.user32.PrintWindow(hwnd, mem_dc.GetSafeHdc(), 0)
        if not result:
            return None
        
        # Get bitmap data
        bitmap_bits = bitmap.GetBitmapBits(True)
        
        # Convert to numpy array
        frame = np.frombuffer(bitmap_bits, dtype=np.uint8)
        frame = frame.reshape((height, width, 4))  # BGRA format
        frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
        
        # Cleanup
        mem_dc.DeleteDC()
        win32gui.ReleaseDC(hwnd, window_dc)
        win32gui.DeleteObject(bitmap.GetHandle())
        
        return frame
    
    def _capture_pil_imagegrab(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """Capture using PIL ImageGrab (fallback method)"""
        pil_imagegrab = self.capture_methods[CaptureMethod.PIL_IMAGEGRAB]
        
        # Capture region
        bbox = (x, y, x + width, y + height)
        image = pil_imagegrab.grab(bbox)
        
        # Convert to numpy array
        frame = np.array(image)
        
        # Convert RGB to BGR for OpenCV compatibility
        if len(frame.shape) == 3 and frame.shape[2] == 3:
            frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        
        return frame
    
    def _update_performance_metrics(self, method: CaptureMethod, success: bool, 
                                  capture_time: float, error: str = ""):
        """Update performance metrics for capture method"""
        if method not in self.performance_metrics:
            return
        
        metrics = self.performance_metrics[method]
        
        if success:
            metrics.frames_captured += 1
            # Update average capture time
            total_time = metrics.avg_capture_time * (metrics.frames_captured - 1) + capture_time
            metrics.avg_capture_time = total_time / metrics.frames_captured
        else:
            metrics.frames_failed += 1
            metrics.last_error = error
        
        # Update success rate
        total_frames = metrics.frames_captured + metrics.frames_failed
        metrics.success_rate = metrics.frames_captured / total_frames if total_frames > 0 else 0.0
    
    def get_performance_report(self) -> str:
        """Generate performance report for all capture methods"""
        report = "\n=== SCREEN CAPTURE PERFORMANCE ===\n"
        
        for method, metrics in self.performance_metrics.items():
            total_frames = metrics.frames_captured + metrics.frames_failed
            report += f"\n{method.value.upper()}:\n"
            report += f"  Success Rate: {metrics.success_rate:.1%}\n"
            report += f"  Avg Capture Time: {metrics.avg_capture_time*1000:.2f}ms\n"
            report += f"  Frames Captured: {metrics.frames_captured}\n"
            report += f"  Frames Failed: {metrics.frames_failed}\n"
            if metrics.last_error:
                report += f"  Last Error: {metrics.last_error}\n"
        
        report += f"\nCurrent Method: {self.current_method.value.upper()}\n"
        return report
    
    def get_optimal_method(self) -> CaptureMethod:
        """Determine optimal capture method based on performance"""
        best_method = self.current_method
        best_score = 0.0
        
        for method, metrics in self.performance_metrics.items():
            if metrics.frames_captured == 0:
                continue
            
            # Score based on success rate and speed (lower capture time is better)
            speed_score = 1.0 / (metrics.avg_capture_time + 0.001)  # Avoid division by zero
            score = metrics.success_rate * speed_score
            
            if score > best_score:
                best_score = score
                best_method = method
        
        return best_method
    
    def cleanup(self):
        """Cleanup capture resources"""
        try:
            # Cleanup MSS
            if CaptureMethod.MSS in self.capture_methods:
                self.capture_methods[CaptureMethod.MSS].close()
            
            # Cleanup Win32 resources
            if CaptureMethod.WIN32_BITBLT in self.capture_methods:
                bitblt_data = self.capture_methods[CaptureMethod.WIN32_BITBLT]
                if bitblt_data.get('screen_dc'):
                    win32gui.ReleaseDC(0, bitblt_data['screen_dc'])
            
            self.logger.info("Screen capture cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Screen capture cleanup failed: {e}")
