#!/usr/bin/env python3
"""
🎯 VISUAL AIMBOT WITH GAME OVERLAY
=================================
Shows real-time visual feedback directly on your game screen:
• Target detection boxes
• Crosshair tracking
• FPS counter
• Detection confidence
• Aim prediction lines
"""

import cv2
import numpy as np
import mss
import time
import threading
import win32api
import win32gui
import win32con
from win32gui import FindWindow, GetWindowRect, SetWindowPos
import tkinter as tk
from tkinter import messagebox
import psutil
import os
import keyboard  # For hotkey detection

class VisualAimbotOverlay:
    def __init__(self):
        # Performance settings
        self.running = False
        self.window_created = False  # Add window creation flag
        self.fps = 0
        self.detections = 0
        self.confidence_threshold = 0.6
        self.fov_size = 400
        self.sensitivity = 0.8

        # Aimbot activation control
        self.aimbot_active = False  # Aimbot starts INACTIVE
        self.activation_key = 'F1'  # Default activation key

        # Visual settings
        self.show_fov = True
        self.show_targets = True
        self.show_crosshair = True
        self.show_stats = True
        self.show_prediction = True
        
        # Colors (BGR format for OpenCV)
        self.colors = {
            'fov': (0, 255, 255),      # Yellow FOV circle
            'target': (0, 0, 255),     # Red target boxes
            'crosshair': (0, 255, 0),  # Green crosshair
            'prediction': (255, 0, 255), # Magenta prediction line
            'text': (255, 255, 255)    # White text
        }
        
        # Screen capture
        self.sct = mss.mss()
        
        # Get screen dimensions
        self.screen_w = win32api.GetSystemMetrics(0)
        self.screen_h = win32api.GetSystemMetrics(1)
        
        print("🎯 Visual Aimbot Overlay initialized")
        print(f"📺 Screen: {self.screen_w}x{self.screen_h}")
        print("🎮 Press 'Q' to quit, 'T' to toggle targets, 'F' to toggle FOV")
        print(f"🔥 Press '{self.activation_key}' to toggle AIMBOT ON/OFF")
        print("⚠️  AIMBOT STARTS INACTIVE - You have full mouse control!")

    def toggle_aimbot(self):
        """Toggle aimbot activation on/off"""
        self.aimbot_active = not self.aimbot_active
        status = "🔥 ACTIVE" if self.aimbot_active else "💤 INACTIVE"
        print(f"\n🎯 AIMBOT {status}")
        if self.aimbot_active:
            print("🎮 Aimbot will now move your mouse to targets!")
        else:
            print("🖱️  You have full mouse control back!")

    def setup_hotkeys(self):
        """Setup hotkey listeners"""
        try:
            keyboard.add_hotkey('f1', self.toggle_aimbot)
            keyboard.add_hotkey('f2', self.open_settings_gui)
            print(f"✅ Hotkey F1 registered for aimbot toggle")
            print(f"✅ Hotkey F2 registered for settings GUI")
        except Exception as e:
            print(f"⚠️  Could not register hotkey: {e}")

    def open_settings_gui(self):
        """Open settings GUI"""
        try:
            from aimbot_settings_gui import AimbotSettingsGUI
            import threading

            gui = AimbotSettingsGUI(self)
            gui_thread = threading.Thread(target=gui.run, daemon=True)
            gui_thread.start()
            print("🎛️ Settings GUI opened!")
        except Exception as e:
            print(f"❌ Could not open settings GUI: {e}")

    def detect_targets(self, frame):
        """Advanced target detection with multiple methods"""
        targets = []
        
        # Convert to different color spaces for better detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
        
        # Method 1: Color-based detection (enemy colors)
        color_ranges = [
            # Red enemies
            ([0, 100, 100], [10, 255, 255]),
            ([170, 100, 100], [180, 255, 255]),
            # Blue enemies  
            ([100, 100, 100], [130, 255, 255]),
            # Yellow/Orange enemies
            ([15, 100, 100], [35, 255, 255])
        ]
        
        for i, (lower, upper) in enumerate(color_ranges):
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            
            # Morphological operations to clean up the mask
            kernel = np.ones((3,3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 50 < area < 5000:  # Filter by size
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Calculate confidence based on area and aspect ratio
                    aspect_ratio = w / h if h > 0 else 0
                    confidence = min((area / 1000.0) * (1.0 if 0.5 < aspect_ratio < 2.0 else 0.5), 1.0)
                    
                    if confidence > self.confidence_threshold:
                        targets.append({
                            'x': x + w // 2,
                            'y': y + h // 2,
                            'w': w,
                            'h': h,
                            'confidence': confidence,
                            'type': f'color_{i}'
                        })
        
        # Method 2: Edge-based detection (for outlined enemies)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            area = cv2.contourArea(contour)
            if 100 < area < 3000:
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h if h > 0 else 0
                
                if 0.3 < aspect_ratio < 3.0:  # Human-like proportions
                    confidence = min(area / 2000.0, 0.8)
                    if confidence > self.confidence_threshold:
                        targets.append({
                            'x': x + w // 2,
                            'y': y + h // 2,
                            'w': w,
                            'h': h,
                            'confidence': confidence,
                            'type': 'edge'
                        })
        
        # Remove duplicate targets (too close to each other)
        filtered_targets = []
        for target in targets:
            is_duplicate = False
            for existing in filtered_targets:
                distance = np.sqrt((target['x'] - existing['x'])**2 + (target['y'] - existing['y'])**2)
                if distance < 30:  # Too close, likely duplicate
                    if target['confidence'] > existing['confidence']:
                        filtered_targets.remove(existing)
                    else:
                        is_duplicate = True
                    break
            
            if not is_duplicate:
                filtered_targets.append(target)
        
        return filtered_targets
    
    def draw_overlay(self, frame, targets, region):
        """Draw all visual overlays on the frame"""
        h, w = frame.shape[:2]
        center_x, center_y = w // 2, h // 2
        
        # Draw FOV circle
        if self.show_fov:
            cv2.circle(frame, (center_x, center_y), self.fov_size // 2, self.colors['fov'], 2)
            cv2.putText(frame, f"FOV: {self.fov_size}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
        
        # Draw crosshair
        if self.show_crosshair:
            cv2.line(frame, (center_x - 20, center_y), (center_x + 20, center_y), self.colors['crosshair'], 2)
            cv2.line(frame, (center_x, center_y - 20), (center_x, center_y + 20), self.colors['crosshair'], 2)
        
        # Draw targets
        if self.show_targets and targets:
            for i, target in enumerate(targets):
                x, y = target['x'], target['y']
                w, h = target['w'], target['h']
                confidence = target['confidence']
                
                # Target box
                cv2.rectangle(frame, (x - w//2, y - h//2), (x + w//2, y + h//2), self.colors['target'], 2)
                
                # Confidence text
                cv2.putText(frame, f"{confidence:.2f}", (x - w//2, y - h//2 - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, self.colors['target'], 1)
                
                # Target center dot
                cv2.circle(frame, (x, y), 3, self.colors['target'], -1)
                
                # Prediction line to target
                if self.show_prediction:
                    cv2.line(frame, (center_x, center_y), (x, y), self.colors['prediction'], 1)
                    
                    # Distance text
                    distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                    cv2.putText(frame, f"{distance:.0f}px", (x + 10, y), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, self.colors['prediction'], 1)
        
        # Draw stats
        if self.show_stats:
            stats_y = 60
            cv2.putText(frame, f"FPS: {self.fps:.0f}", (10, stats_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
            
            stats_y += 30
            cv2.putText(frame, f"Targets: {len(targets)}", (10, stats_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
            
            stats_y += 30
            cv2.putText(frame, f"Total Detections: {self.detections}", (10, stats_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
            
            # Aimbot activation status
            stats_y += 30
            if self.aimbot_active:
                status = "🔥 AIMBOT: ACTIVE"
                status_color = (0, 255, 0)  # Green when active
            else:
                status = "💤 AIMBOT: INACTIVE"
                status_color = (0, 0, 255)  # Red when inactive
            cv2.putText(frame, status, (10, stats_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)

            # Hotkey reminder
            stats_y += 30
            cv2.putText(frame, f"Press {self.activation_key} to toggle", (10, stats_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, self.colors['text'], 1)
        
        return frame
    
    def move_mouse_to_target(self, target, region):
        """Move mouse to the best target - ONLY if aimbot is active"""
        if not target or not self.aimbot_active:
            return  # Don't move mouse if aimbot is inactive

        # Calculate screen coordinates
        target_screen_x = region['left'] + target['x']
        target_screen_y = region['top'] + target['y']

        # Get current mouse position
        current_x, current_y = win32gui.GetCursorPos()

        # Apply smoothing and sensitivity
        dx = (target_screen_x - current_x) * self.sensitivity
        dy = (target_screen_y - current_y) * self.sensitivity

        new_x = int(current_x + dx)
        new_y = int(current_y + dy)

        # Move mouse
        win32api.SetCursorPos((new_x, new_y))
    
    def run(self):
        """Main aimbot loop with visual overlay"""
        self.running = True

        print("🚀 Starting Visual Aimbot Overlay...")
        print("🎮 You should see the overlay on your screen!")

        # Setup hotkeys
        self.setup_hotkeys()

        frame_count = 0
        start_time = time.time()
        
        try:
            while self.running:
                loop_start = time.perf_counter()
                
                # Capture screen region around crosshair
                center_x = self.screen_w // 2
                center_y = self.screen_h // 2
                
                region = {
                    'left': center_x - self.fov_size // 2,
                    'top': center_y - self.fov_size // 2,
                    'width': self.fov_size,
                    'height': self.fov_size
                }
                
                # Capture screenshot
                screenshot = self.sct.grab(region)
                frame = np.array(screenshot)
                frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
                
                # Detect targets
                targets = self.detect_targets(frame)
                
                if targets:
                    self.detections += len(targets)
                    # Move to best target (highest confidence)
                    best_target = max(targets, key=lambda t: t['confidence'])
                    self.move_mouse_to_target(best_target, region)
                
                # Draw overlay
                overlay_frame = self.draw_overlay(frame, targets, region)

                # Create window only once with proper cleanup
                window_name = 'AIMBOT_OVERLAY'
                if not self.window_created:
                    # Destroy any existing windows first
                    cv2.destroyAllWindows()
                    cv2.waitKey(1)  # Allow time for cleanup

                    # Create new window
                    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO)
                    cv2.resizeWindow(window_name, 600, 600)
                    cv2.moveWindow(window_name, 100, 100)  # Position window
                    self.window_created = True
                    print("✅ Overlay window created")

                # Update the existing window
                cv2.imshow(window_name, overlay_frame)
                
                # Handle key presses
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q') or key == ord('Q'):
                    break
                elif key == ord('t') or key == ord('T'):
                    self.show_targets = not self.show_targets
                    print(f"Target display: {'ON' if self.show_targets else 'OFF'}")
                elif key == ord('f') or key == ord('F'):
                    self.show_fov = not self.show_fov
                    print(f"FOV display: {'ON' if self.show_fov else 'OFF'}")
                elif key == ord('c') or key == ord('C'):
                    self.show_crosshair = not self.show_crosshair
                    print(f"Crosshair: {'ON' if self.show_crosshair else 'OFF'}")
                elif key == ord('s') or key == ord('S'):
                    self.open_settings_gui()
                
                # Calculate FPS
                frame_count += 1
                if frame_count % 30 == 0:
                    elapsed = time.time() - start_time
                    self.fps = frame_count / elapsed
                
                # Small delay to prevent excessive CPU usage
                time.sleep(0.001)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopping aimbot...")
        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            self.running = False
            cv2.destroyAllWindows()
            print("👋 Visual Aimbot Overlay stopped")

def main():
    print("🎯 VISUAL AIMBOT WITH GAME OVERLAY")
    print("=" * 40)
    print("This will show you EXACTLY what the aimbot sees!")
    print("• Red boxes around detected targets")
    print("• Yellow FOV circle")
    print("• Green crosshair")
    print("• Real-time FPS and detection stats")
    print("• Magenta prediction lines")
    print()
    print("Controls:")
    print("• F1: 🔥 TOGGLE AIMBOT ON/OFF (starts INACTIVE)")
    print("• Q: Quit")
    print("• T: Toggle target display")
    print("• F: Toggle FOV circle")
    print("• C: Toggle crosshair")
    print("• S: Open Settings GUI")
    print()
    print("⚠️  IMPORTANT: Aimbot starts INACTIVE!")
    print("   Press F1 to activate mouse movement.")
    print("   Visual overlay always shows regardless of activation.")
    print()

    try:
        aimbot = VisualAimbotOverlay()

        # Option to launch with settings GUI
        import sys
        if '--with-gui' in sys.argv or input("Launch with Settings GUI? (y/n): ").lower() == 'y':
            from aimbot_settings_gui import AimbotSettingsGUI
            import threading

            # Start settings GUI in separate thread
            gui = AimbotSettingsGUI(aimbot)
            gui_thread = threading.Thread(target=gui.run, daemon=True)
            gui_thread.start()
            print("🎛️ Settings GUI launched!")

        aimbot.run()
    except Exception as e:
        print(f"❌ Failed to start: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
