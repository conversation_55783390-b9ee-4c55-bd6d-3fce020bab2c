#!/usr/bin/env python3
"""
🎛️ AIMBOT SETTINGS GUI
======================
Comprehensive settings interface for the visual aimbot
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from threading import Thread
import time

class AimbotSettingsGUI:
    def __init__(self, aimbot_instance=None):
        self.aimbot = aimbot_instance
        self.root = tk.Tk()
        self.root.title("🎯 Aimbot Settings Control Panel")
        self.root.geometry("800x700")
        self.root.configure(bg='#2b2b2b')
        
        # Settings storage
        self.settings_file = "aimbot_settings.json"
        self.current_settings = self.load_default_settings()
        
        # Create GUI
        self.create_widgets()
        self.load_settings()
        
        # Auto-apply settings
        self.auto_apply_enabled = tk.BooleanVar(value=True)
        
    def load_default_settings(self):
        """Load default settings"""
        return {
            'fov_size': 400,
            'sensitivity': 0.8,
            'confidence_threshold': 0.6,
            'smoothing': 0.8,
            'show_fov': True,
            'show_targets': True,
            'show_crosshair': True,
            'show_stats': True,
            'show_prediction': True,
            'activation_key': 'F1',
            'detection_method': 'Color + Edge'
        }
    
    def create_widgets(self):
        """Create all GUI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="🎯 AIMBOT CONTROL PANEL", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Status frame
        self.create_status_frame(main_frame, row=1)
        
        # Settings notebook
        notebook = ttk.Notebook(main_frame)
        notebook.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        # Performance tab
        perf_frame = ttk.Frame(notebook, padding="10")
        notebook.add(perf_frame, text="🎯 Performance")
        self.create_performance_tab(perf_frame)
        
        # Visual tab
        visual_frame = ttk.Frame(notebook, padding="10")
        notebook.add(visual_frame, text="👁️ Visual")
        self.create_visual_tab(visual_frame)
        
        # Controls tab
        controls_frame = ttk.Frame(notebook, padding="10")
        notebook.add(controls_frame, text="🎮 Controls")
        self.create_controls_tab(controls_frame)
        
        # Buttons frame
        self.create_buttons_frame(main_frame, row=3)
        
    def create_status_frame(self, parent, row):
        """Create status display frame"""
        status_frame = ttk.LabelFrame(parent, text="📊 Current Status", padding="10")
        status_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Aimbot status
        self.status_label = ttk.Label(status_frame, text="💤 AIMBOT: INACTIVE", 
                                     font=('Arial', 12, 'bold'), foreground='red')
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # Connection status
        self.connection_label = ttk.Label(status_frame, text="🔗 Connection: Not Connected", 
                                         font=('Arial', 10))
        self.connection_label.grid(row=1, column=0, sticky=tk.W)
        
    def create_performance_tab(self, parent):
        """Create performance settings tab"""
        # FOV Size
        ttk.Label(parent, text="🎯 FOV Size:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.fov_var = tk.IntVar(value=400)
        fov_scale = ttk.Scale(parent, from_=200, to=800, variable=self.fov_var, 
                             orient=tk.HORIZONTAL, length=300, command=self.on_setting_change)
        fov_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=10)
        self.fov_label = ttk.Label(parent, text="400")
        self.fov_label.grid(row=0, column=2, sticky=tk.W)
        
        # Sensitivity
        ttk.Label(parent, text="🖱️ Mouse Sensitivity:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.sens_var = tk.DoubleVar(value=0.8)
        sens_scale = ttk.Scale(parent, from_=0.1, to=2.0, variable=self.sens_var, 
                              orient=tk.HORIZONTAL, length=300, command=self.on_setting_change)
        sens_scale.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=10)
        self.sens_label = ttk.Label(parent, text="0.8")
        self.sens_label.grid(row=1, column=2, sticky=tk.W)
        
        # Confidence Threshold
        ttk.Label(parent, text="🎯 Confidence Threshold:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.conf_var = tk.DoubleVar(value=0.6)
        conf_scale = ttk.Scale(parent, from_=0.1, to=1.0, variable=self.conf_var, 
                              orient=tk.HORIZONTAL, length=300, command=self.on_setting_change)
        conf_scale.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=10)
        self.conf_label = ttk.Label(parent, text="0.6")
        self.conf_label.grid(row=2, column=2, sticky=tk.W)
        
        # Smoothing
        ttk.Label(parent, text="🌊 Mouse Smoothing:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.smooth_var = tk.DoubleVar(value=0.8)
        smooth_scale = ttk.Scale(parent, from_=0.1, to=1.0, variable=self.smooth_var, 
                                orient=tk.HORIZONTAL, length=300, command=self.on_setting_change)
        smooth_scale.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=10)
        self.smooth_label = ttk.Label(parent, text="0.8")
        self.smooth_label.grid(row=3, column=2, sticky=tk.W)
        
    def create_visual_tab(self, parent):
        """Create visual settings tab"""
        # Visual toggles
        self.show_fov_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(parent, text="🔵 Show FOV Circle", variable=self.show_fov_var,
                       command=self.on_setting_change).grid(row=0, column=0, sticky=tk.W, pady=5)
        
        self.show_targets_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(parent, text="🎯 Show Target Boxes", variable=self.show_targets_var,
                       command=self.on_setting_change).grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.show_crosshair_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(parent, text="➕ Show Crosshair", variable=self.show_crosshair_var,
                       command=self.on_setting_change).grid(row=2, column=0, sticky=tk.W, pady=5)
        
        self.show_stats_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(parent, text="📊 Show Statistics", variable=self.show_stats_var,
                       command=self.on_setting_change).grid(row=3, column=0, sticky=tk.W, pady=5)
        
        self.show_prediction_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(parent, text="🔮 Show Prediction Lines", variable=self.show_prediction_var,
                       command=self.on_setting_change).grid(row=4, column=0, sticky=tk.W, pady=5)
        
    def create_controls_tab(self, parent):
        """Create controls settings tab"""
        # Activation key
        ttk.Label(parent, text="🔥 Activation Hotkey:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.hotkey_var = tk.StringVar(value='F1')
        hotkey_combo = ttk.Combobox(parent, textvariable=self.hotkey_var, 
                                   values=['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8'],
                                   state='readonly', width=10)
        hotkey_combo.grid(row=0, column=1, sticky=tk.W, padx=10)
        hotkey_combo.bind('<<ComboboxSelected>>', self.on_setting_change)
        
        # Detection method
        ttk.Label(parent, text="🔍 Detection Method:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.detection_var = tk.StringVar(value='Color + Edge')
        detection_combo = ttk.Combobox(parent, textvariable=self.detection_var,
                                      values=['Color Only', 'Edge Only', 'Color + Edge', 'Advanced'],
                                      state='readonly', width=15)
        detection_combo.grid(row=1, column=1, sticky=tk.W, padx=10)
        detection_combo.bind('<<ComboboxSelected>>', self.on_setting_change)
        
    def create_buttons_frame(self, parent, row):
        """Create buttons frame"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.grid(row=row, column=0, columnspan=3, pady=20)
        
        # Auto-apply checkbox
        ttk.Checkbutton(buttons_frame, text="🔄 Auto-apply changes", 
                       variable=self.auto_apply_enabled).grid(row=0, column=0, columnspan=4, pady=5)
        
        # Buttons
        ttk.Button(buttons_frame, text="💾 Save Settings", 
                  command=self.save_settings).grid(row=1, column=0, padx=5)
        ttk.Button(buttons_frame, text="📁 Load Settings", 
                  command=self.load_settings_file).grid(row=1, column=1, padx=5)
        ttk.Button(buttons_frame, text="🔄 Apply Now", 
                  command=self.apply_settings).grid(row=1, column=2, padx=5)
        ttk.Button(buttons_frame, text="🔧 Reset Defaults", 
                  command=self.reset_defaults).grid(row=1, column=3, padx=5)
        
    def on_setting_change(self, *args):
        """Handle setting changes"""
        # Update labels
        self.fov_label.config(text=str(int(self.fov_var.get())))
        self.sens_label.config(text=f"{self.sens_var.get():.2f}")
        self.conf_label.config(text=f"{self.conf_var.get():.2f}")
        self.smooth_label.config(text=f"{self.smooth_var.get():.2f}")
        
        # Auto-apply if enabled
        if self.auto_apply_enabled.get():
            self.apply_settings()
    
    def apply_settings(self):
        """Apply current settings to aimbot"""
        if not self.aimbot:
            return
            
        try:
            # Apply performance settings
            self.aimbot.fov_size = int(self.fov_var.get())
            self.aimbot.sensitivity = self.sens_var.get()
            self.aimbot.confidence_threshold = self.conf_var.get()
            self.aimbot.smoothing = self.smooth_var.get()
            
            # Apply visual settings
            self.aimbot.show_fov = self.show_fov_var.get()
            self.aimbot.show_targets = self.show_targets_var.get()
            self.aimbot.show_crosshair = self.show_crosshair_var.get()
            self.aimbot.show_stats = self.show_stats_var.get()
            self.aimbot.show_prediction = self.show_prediction_var.get()
            
            # Apply control settings
            self.aimbot.activation_key = self.hotkey_var.get()
            
            print("✅ Settings applied to aimbot")
        except Exception as e:
            print(f"❌ Error applying settings: {e}")
    
    def save_settings(self):
        """Save current settings to file"""
        settings = {
            'fov_size': int(self.fov_var.get()),
            'sensitivity': self.sens_var.get(),
            'confidence_threshold': self.conf_var.get(),
            'smoothing': self.smooth_var.get(),
            'show_fov': self.show_fov_var.get(),
            'show_targets': self.show_targets_var.get(),
            'show_crosshair': self.show_crosshair_var.get(),
            'show_stats': self.show_stats_var.get(),
            'show_prediction': self.show_prediction_var.get(),
            'activation_key': self.hotkey_var.get(),
            'detection_method': self.detection_var.get()
        }
        
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(settings, f, indent=4)
            messagebox.showinfo("Success", "Settings saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {e}")
    
    def load_settings_file(self):
        """Load settings from file"""
        filename = filedialog.askopenfilename(
            title="Load Settings",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r') as f:
                    settings = json.load(f)
                self.load_settings_dict(settings)
                messagebox.showinfo("Success", "Settings loaded successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load settings: {e}")
    
    def load_settings(self):
        """Load settings from default file"""
        if os.path.exists(self.settings_file):
            try:
                with open(self.settings_file, 'r') as f:
                    settings = json.load(f)
                self.load_settings_dict(settings)
            except Exception as e:
                print(f"Warning: Could not load settings: {e}")
    
    def load_settings_dict(self, settings):
        """Load settings from dictionary"""
        self.fov_var.set(settings.get('fov_size', 400))
        self.sens_var.set(settings.get('sensitivity', 0.8))
        self.conf_var.set(settings.get('confidence_threshold', 0.6))
        self.smooth_var.set(settings.get('smoothing', 0.8))
        self.show_fov_var.set(settings.get('show_fov', True))
        self.show_targets_var.set(settings.get('show_targets', True))
        self.show_crosshair_var.set(settings.get('show_crosshair', True))
        self.show_stats_var.set(settings.get('show_stats', True))
        self.show_prediction_var.set(settings.get('show_prediction', True))
        self.hotkey_var.set(settings.get('activation_key', 'F1'))
        self.detection_var.set(settings.get('detection_method', 'Color + Edge'))
        
        # Update labels
        self.on_setting_change()
    
    def reset_defaults(self):
        """Reset to default settings"""
        if messagebox.askyesno("Reset", "Reset all settings to defaults?"):
            self.load_settings_dict(self.load_default_settings())
    
    def update_status(self):
        """Update status display"""
        if self.aimbot:
            if hasattr(self.aimbot, 'aimbot_active'):
                if self.aimbot.aimbot_active:
                    self.status_label.config(text="🔥 AIMBOT: ACTIVE", foreground='green')
                else:
                    self.status_label.config(text="💤 AIMBOT: INACTIVE", foreground='red')
            
            self.connection_label.config(text="🔗 Connection: Connected", foreground='green')
        else:
            self.connection_label.config(text="🔗 Connection: Not Connected", foreground='red')
        
        # Schedule next update
        self.root.after(1000, self.update_status)
    
    def run(self):
        """Run the GUI"""
        self.update_status()
        self.root.mainloop()

if __name__ == "__main__":
    gui = AimbotSettingsGUI()
    gui.run()
