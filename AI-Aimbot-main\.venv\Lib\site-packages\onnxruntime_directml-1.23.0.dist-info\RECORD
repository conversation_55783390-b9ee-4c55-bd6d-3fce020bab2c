../../Scripts/onnxruntime_test.exe,sha256=9_uKJtIIdweizLPBQhoM2L0DGOBHJKw6GyqQpHWRdSc,108421
onnxruntime/LICENSE,sha256=wlDWJ48LR6ZDn7dZKwi1ilXrn1NapJodtjIRw_mCtnQ,1094
onnxruntime/Privacy.md,sha256=v7dxKwdfPwfj6-5dwqKW0d4y2_ca0oZj9z0VOMtsOwg,2490
onnxruntime/ThirdPartyNotices.txt,sha256=4A-Cjgoz3lkaNVrmYG0mJfV1jafSyETbeCHJ3T42R7Y,333022
onnxruntime/__init__.py,sha256=mE1tr2wVVmzB66KwvL_XuyguD8eFj13BY4YUqzPvE4M,15071
onnxruntime/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/backend/__init__.py,sha256=5I1Ylsawf9w6MNmK4RiN1wA-EEQqlKKwYTNZB-m_k6M,334
onnxruntime/backend/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/backend/__pycache__/backend.cpython-311.pyc,,
onnxruntime/backend/__pycache__/backend_rep.cpython-311.pyc,,
onnxruntime/backend/backend.py,sha256=xPA69Lf7rwwwgeWoZ3CgB2JSoExuIAdhHmd6ROp19sc,8187
onnxruntime/backend/backend_rep.py,sha256=A7S4GqxLC6IfkbEXLlWiWpCD9AJ5x-xAhnR8BCM2cNk,1776
onnxruntime/capi/DirectML.dll,sha256=BImoVBULFxyetklcOMfmNjyVKBUbnNrgZs_ncKGP1bQ,18527784
onnxruntime/capi/__init__.py,sha256=uRp4pMtfoayBhZgEsiFqFCD13Y6LUo82FdZsQX8X8LI,251
onnxruntime/capi/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/capi/__pycache__/_ld_preload.cpython-311.pyc,,
onnxruntime/capi/__pycache__/_pybind_state.cpython-311.pyc,,
onnxruntime/capi/__pycache__/build_and_package_info.cpython-311.pyc,,
onnxruntime/capi/__pycache__/convert_npz_to_onnx_adapter.cpython-311.pyc,,
onnxruntime/capi/__pycache__/onnxruntime_collect_build_info.cpython-311.pyc,,
onnxruntime/capi/__pycache__/onnxruntime_inference_collection.cpython-311.pyc,,
onnxruntime/capi/__pycache__/onnxruntime_validation.cpython-311.pyc,,
onnxruntime/capi/__pycache__/version_info.cpython-311.pyc,,
onnxruntime/capi/_ld_preload.py,sha256=li6cbZ64hDfUndat4mprUWzowLa3RQdw0q2E56sXFwE,413
onnxruntime/capi/_pybind_state.py,sha256=nbUpnUncwBv5pgJA8yugDYJRA4TTfC0gaYOED5jD-SA,1533
onnxruntime/capi/build_and_package_info.py,sha256=cxPS7zV6_wvbZnFeN_FOGRCpWE7u9BYB34e1_lZFG8E,63
onnxruntime/capi/convert_npz_to_onnx_adapter.py,sha256=N0ShYr30vBQcOr9KyFd4AUdEcqWW89KVd80qSYCgdQ4,1581
onnxruntime/capi/onnxruntime.dll,sha256=o1ji7AUaiUgJH-50xqg29h6p6X-M4RcbkmHrstojfsk,21056056
onnxruntime/capi/onnxruntime_collect_build_info.py,sha256=sD8Z2S15QHSvuO1j7tgqJKeORDUatwzUmzvC8Uj9cAM,2109
onnxruntime/capi/onnxruntime_inference_collection.py,sha256=0-ISc_Bo8U3G8j5P-dZjRS0eSQ0vpDVSEODNmwJptNA,59993
onnxruntime/capi/onnxruntime_providers_shared.dll,sha256=Qyw0cVMIw7f6YwbyZbJlg3pdMvWhrlKYfPXDiiRRsJ0,21552
onnxruntime/capi/onnxruntime_pybind11_state.pyd,sha256=Yn4OwHHAKbx9adoz68-27SgXT9kDrA4ZbOhnHGKusuQ,26076728
onnxruntime/capi/onnxruntime_validation.py,sha256=nJydkJxSVNiRqQvVkJCpfFZoeZAvATFD8OIze3SY_5E,6865
onnxruntime/capi/version_info.py,sha256=8mm1VTXF8xgx6N8vFNe0Tiik9qdg9Vvi9f32bPE9ktw,34
onnxruntime/datasets/__init__.py,sha256=DqRdpMfRtDfhVkCQu5lTmfSQ-GG4dETHNWdoB4fA7lU,473
onnxruntime/datasets/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/datasets/logreg_iris.onnx,sha256=giR4TJjXNBLZ_ZmrzVejhWi9WQmA0PvlkWRkUxxS6Pw,670
onnxruntime/datasets/mul_1.onnx,sha256=cfQxxOkyHsb76xWNAu0kBFmn3MmGc_p5pPQ5zkLvrxA,130
onnxruntime/datasets/sigmoid.onnx,sha256=U0Crpnp-NHUWKteUN4r1XxcY9V-aXXS0r2Dsx_emJLY,103
onnxruntime/quantization/CalTableFlatBuffers/KeyValue.py,sha256=e-jJFhw9fb775fDCLnWdbRSdoJ6vGD0c7qTnkIG-vNs,2250
onnxruntime/quantization/CalTableFlatBuffers/TrtTable.py,sha256=vrueLPy2RV5R3MSdIQkqW7TxDSSKPMoOixSuzIsv2oE,2682
onnxruntime/quantization/CalTableFlatBuffers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/quantization/CalTableFlatBuffers/__pycache__/KeyValue.cpython-311.pyc,,
onnxruntime/quantization/CalTableFlatBuffers/__pycache__/TrtTable.cpython-311.pyc,,
onnxruntime/quantization/CalTableFlatBuffers/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/__init__.py,sha256=gL1o5I5h988WNCfzJ1KMpSAvk2xjgw0HcqYsfXcKurw,647
onnxruntime/quantization/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/base_quantizer.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/calibrate.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/matmul_bnb4_quantizer.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/matmul_nbits_quantizer.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/onnx_model.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/onnx_quantizer.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/preprocess.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/qdq_loss_debug.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/qdq_quantizer.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/quant_utils.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/quantize.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/registry.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/shape_inference.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/static_quantize_runner.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/tensor_quant_overrides.cpython-311.pyc,,
onnxruntime/quantization/base_quantizer.py,sha256=Ur85CgEWLdi2AZ2OcUOQkH2xEWJ8wa1bBu20zX1WElY,26179
onnxruntime/quantization/calibrate.py,sha256=1y7nZT1E9khhwnppADjBdcm9FjogjUJo8sDUxel-bZU,54020
onnxruntime/quantization/execution_providers/qnn/__init__.py,sha256=nKKB7VEbO574HDL2xdJPD8VeXoK2a3jd8nLBxULiVvI,120
onnxruntime/quantization/execution_providers/qnn/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/fusion_lpnorm.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/fusion_spacetodepth.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/mixed_precision_overrides_utils.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/preprocess.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/quant_config.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/fusion_lpnorm.py,sha256=vUrbMNorHH7_uKjeL1jlkPghnplPIDPz0kmN0Tt03mc,5327
onnxruntime/quantization/execution_providers/qnn/fusion_spacetodepth.py,sha256=u_OyHhp519VoBhUVZTAVkey501E91U0EYMkCUmNwMTE,6285
onnxruntime/quantization/execution_providers/qnn/mixed_precision_overrides_utils.py,sha256=PTdZPaP1cZAUXu14e4L0j71wO53lcD1s8yPkZlan5G0,18995
onnxruntime/quantization/execution_providers/qnn/preprocess.py,sha256=TxXA9-8SBqOJzko4wrxB60rJjxNLbXViMkJSmaKJ8CM,15698
onnxruntime/quantization/execution_providers/qnn/quant_config.py,sha256=TjlRcNUw109mBWd_NfEUQn3UQLRt1B2dyT63g-LNba0,19835
onnxruntime/quantization/fusions/__init__.py,sha256=QDG2mzyaAHnVmHkOBpc4Mdwn5NcNrattW5yTavMWs8M,246
onnxruntime/quantization/fusions/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/fusions/__pycache__/fusion.cpython-311.pyc,,
onnxruntime/quantization/fusions/__pycache__/fusion_gelu.cpython-311.pyc,,
onnxruntime/quantization/fusions/__pycache__/fusion_layernorm.cpython-311.pyc,,
onnxruntime/quantization/fusions/__pycache__/replace_upsample_with_resize.cpython-311.pyc,,
onnxruntime/quantization/fusions/fusion.py,sha256=A6_77l5uw-hIwyoX7DPOFL6O-y3qXk-S16SMLv1Ncis,12088
onnxruntime/quantization/fusions/fusion_gelu.py,sha256=3qOO4U95ATD6S14dyC-5-vGeaQBr5U-GCsjfvHqoL98,10647
onnxruntime/quantization/fusions/fusion_layernorm.py,sha256=CKU--IH-xDUnm5qZtTK1ENYuBMnPsADUkzrOBjyW7kQ,5306
onnxruntime/quantization/fusions/replace_upsample_with_resize.py,sha256=Ewuca08Yu_6XQMock17J20oRIaoHwLzONx3Nd0bGS_A,3427
onnxruntime/quantization/matmul_bnb4_quantizer.py,sha256=aXTPtK3uQ80YTeSnf_bvBITqcoTnvxNCzJltA4ZS8iM,9263
onnxruntime/quantization/matmul_nbits_quantizer.py,sha256=Wi-2e4S_VDrmmjiOV_zHDHdYhR4Ra0cdOvpDAMiBYG8,68463
onnxruntime/quantization/neural_compressor/__init__.py,sha256=4SEilQJJDoKxMpoontHVtxLctL4kzZD50jiHylxeMWQ,68
onnxruntime/quantization/neural_compressor/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/neural_compressor/__pycache__/onnx_model.cpython-311.pyc,,
onnxruntime/quantization/neural_compressor/__pycache__/util.cpython-311.pyc,,
onnxruntime/quantization/neural_compressor/__pycache__/weight_only.cpython-311.pyc,,
onnxruntime/quantization/neural_compressor/onnx_model.py,sha256=iNsKGuRhkFo1xTed2sunrbAM4I-ZMiLWRcU7yIeRWi0,51478
onnxruntime/quantization/neural_compressor/util.py,sha256=4AbSArXCHxT8MTPhRn3rp-eysd9aju5ciha1CDMW2D4,2762
onnxruntime/quantization/neural_compressor/weight_only.py,sha256=I623O0GdFuKwgQUvh4txNWPIbvZ5HYjMSDzdOv83uE4,37227
onnxruntime/quantization/onnx_model.py,sha256=Eabr8tVPnaYCZnsqW3d8Fd0Hm28OZbqsoYZHNPqSEFY,24528
onnxruntime/quantization/onnx_quantizer.py,sha256=7cK-89RJ016NDCMtOc7i7WdXWewH_te166xjJ-3_DBM,50385
onnxruntime/quantization/operators/__init__.py,sha256=IfKXrFWtRSye1mkgD9lpwxio0fw9cVr_1CdV1cvefig,85
onnxruntime/quantization/operators/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/activation.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/argmax.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/attention.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/base_operator.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/binary_op.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/concat.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/conv.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/direct_q8.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/embed_layernorm.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/gather.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/gavgpool.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/gemm.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/lstm.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/matmul.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/maxpool.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/norm.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/pad.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/pooling.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/qdq_base_operator.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/resize.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/softmax.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/split.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/where.cpython-311.pyc,,
onnxruntime/quantization/operators/activation.py,sha256=G6XAKZaRIV3vrtjpK4kEvbzUSlLOwwEauXX9exgIHZg,4545
onnxruntime/quantization/operators/argmax.py,sha256=pfE9_eSTZ2otTkUcWwlLi7HJKtN10kE5c2Lz0SeVADQ,589
onnxruntime/quantization/operators/attention.py,sha256=eH7-Z3MfP6xRZCdhDAyNxWG2s2nZILxIEFVAHtqj7EQ,2637
onnxruntime/quantization/operators/base_operator.py,sha256=vrAVfKJXZvF7ZherKw4JUGonNyNuoU2TWnwBy-EQ3QE,1118
onnxruntime/quantization/operators/binary_op.py,sha256=pEQHRAS75EMp7LG6jzWV7gDQt_vzEPLJEI00eIOuoiA,2544
onnxruntime/quantization/operators/concat.py,sha256=fZFwnaqoOZ9b0ZvGpBK_MrJzVteeJguWRQ396kUh8QQ,2143
onnxruntime/quantization/operators/conv.py,sha256=whgjZwx8iWCUDhiGoMfZ8ADzj9lMzIamYrY2Ps7l8oU,10203
onnxruntime/quantization/operators/direct_q8.py,sha256=xWWLXoO1fJ0FXOFs0OdX5pvLVw3m7b6TBpU1YRYycO0,3389
onnxruntime/quantization/operators/embed_layernorm.py,sha256=2LsZk5Um0ELaRESWjScgYyQioJelRZK6oQbzAclSgXI,4058
onnxruntime/quantization/operators/gather.py,sha256=HL79Csv--zTAs5XmG1xP7Fjw72RTlqpadxB6w-OmqN8,2230
onnxruntime/quantization/operators/gavgpool.py,sha256=wYyjEf3h-_QChWKnsZ2N-haBG1RSvqRitZ-Yvfwo9Dk,2445
onnxruntime/quantization/operators/gemm.py,sha256=SaqWitL-QotPO9KS6A4iZ5H_OUJ7dC_ZQhr5x6epGDQ,6226
onnxruntime/quantization/operators/lstm.py,sha256=gO3AqC3tvoiVlGRDKOLrBRPRyEfnC2EfazcMLOZ4AkE,5238
onnxruntime/quantization/operators/matmul.py,sha256=Z98R4saKkbQhps_jRCCJ91cmptxc8y7b5TmG3gvHIcw,8499
onnxruntime/quantization/operators/maxpool.py,sha256=QyDmHyBo0QKf6kNFbp2a9v6ThrBO-OL3tW0PFdN6bkI,961
onnxruntime/quantization/operators/norm.py,sha256=f3fUiSN4WdM8iuXCSjETorRyDFUSJJWxO10vegj_dEs,1649
onnxruntime/quantization/operators/pad.py,sha256=yM83jrZC88p5Fa53w3KLQc7zIlSz6Gv1iSOPvq5guM4,7951
onnxruntime/quantization/operators/pooling.py,sha256=L0IT7G6-2XSx9-wUz5BX59Mc43FfJEg79NwW3yqEDhI,2285
onnxruntime/quantization/operators/qdq_base_operator.py,sha256=Fco9JZxrXQoVgjKvmHFuzT0mogWo9-wHiDa51CjTioo,823
onnxruntime/quantization/operators/resize.py,sha256=BMeym-7GHOSnGpZisa9BkdQkVmCXwKANA5NpnKRnaLI,962
onnxruntime/quantization/operators/softmax.py,sha256=e3ThVOh2TG1B8luG6xWkoT_hCdvsMRjvTlTje8CW-YQ,2714
onnxruntime/quantization/operators/split.py,sha256=82R65-_Rw5g23f0uekUWpA3nhOzeUWdOQgr2JZXwrOc,2258
onnxruntime/quantization/operators/where.py,sha256=wd6PQ7LlbrJTqamFMch_Fipnbt4IewMJSAPozMTrwKI,3127
onnxruntime/quantization/preprocess.py,sha256=VU4iX7g8gOgVH0zehOcOXsVWkZpx6kG_LFlwGM3Bs6c,5045
onnxruntime/quantization/qdq_loss_debug.py,sha256=1L-BuA1Jck9J5lgRoj7QPEbtaKHMkoQLMTD4iYFOaxo,15829
onnxruntime/quantization/qdq_quantizer.py,sha256=4UiRF83h_G6TIwe7jVuyDJ5gmugK7fV2ZYs79EEk7i0,71380
onnxruntime/quantization/quant_utils.py,sha256=Ypi_EDYAuAA32FkibYBAzU-UgZnsWb4VRjcCtCnVmbs,42156
onnxruntime/quantization/quantize.py,sha256=KLsgW2OKThDkXMg4EbX9SUF2YqpQmt90IZM1YhH50fw,53809
onnxruntime/quantization/registry.py,sha256=zGypcx9sBEcTk5vCJtoO69oqM90rwOtosy8Mitvkcu8,3824
onnxruntime/quantization/shape_inference.py,sha256=KsSE3meIYp5Gd4AD2I_YkmEdvyL71x2vhp017sGlJ_Y,10189
onnxruntime/quantization/static_quantize_runner.py,sha256=SA3B1PrpfUBZeKZC9swAsuKEBTgH_RvOBf8-6UYwpp4,11315
onnxruntime/quantization/tensor_quant_overrides.py,sha256=Qz2D0NAI72xLA5aXqDfZgBXMYpU5jh5HnCfe-RnLyk0,21304
onnxruntime/tools/__init__.py,sha256=7up7iKcklVy6UcpIIIIlBaK690O32vaOxyaaTWvwyxU,528
onnxruntime/tools/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/__pycache__/check_onnx_model_mobile_usability.cpython-311.pyc,,
onnxruntime/tools/__pycache__/convert_onnx_models_to_ort.cpython-311.pyc,,
onnxruntime/tools/__pycache__/file_utils.cpython-311.pyc,,
onnxruntime/tools/__pycache__/logger.cpython-311.pyc,,
onnxruntime/tools/__pycache__/make_dynamic_shape_fixed.cpython-311.pyc,,
onnxruntime/tools/__pycache__/offline_tuning.cpython-311.pyc,,
onnxruntime/tools/__pycache__/onnx_model_utils.cpython-311.pyc,,
onnxruntime/tools/__pycache__/onnx_randomizer.cpython-311.pyc,,
onnxruntime/tools/__pycache__/onnxruntime_test.cpython-311.pyc,,
onnxruntime/tools/__pycache__/optimize_onnx_model.cpython-311.pyc,,
onnxruntime/tools/__pycache__/pytorch_export_contrib_ops.cpython-311.pyc,,
onnxruntime/tools/__pycache__/pytorch_export_helpers.cpython-311.pyc,,
onnxruntime/tools/__pycache__/reduced_build_config_parser.cpython-311.pyc,,
onnxruntime/tools/__pycache__/remove_initializer_from_input.cpython-311.pyc,,
onnxruntime/tools/__pycache__/symbolic_shape_infer.cpython-311.pyc,,
onnxruntime/tools/__pycache__/update_onnx_opset.cpython-311.pyc,,
onnxruntime/tools/check_onnx_model_mobile_usability.py,sha256=hzqjrI9Xz7LP1idPdviRU7EEeLMvUB0qlyTqHofxlFs,1717
onnxruntime/tools/convert_onnx_models_to_ort.py,sha256=FJIv_geqXwObnTEH5T90NM7_NhVfxjf_jXKrDzlJoWY,16939
onnxruntime/tools/file_utils.py,sha256=HrNK4UjpR-u43tLBC2vwKieisCVRBSFnaIDKaswwEGI,1572
onnxruntime/tools/logger.py,sha256=s3M5-Akb69zubXNhCpsjIoJ052gYieHV5FsOfBZ6lrI,333
onnxruntime/tools/make_dynamic_shape_fixed.py,sha256=28zdPjT8yz8AEv5hkFl_YNI8qQzbmP64hl-GqXO-Or0,2642
onnxruntime/tools/mobile_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/mobile_helpers/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/mobile_helpers/__pycache__/usability_checker.cpython-311.pyc,,
onnxruntime/tools/mobile_helpers/coreml_supported_mlprogram_ops.md,sha256=cx6qWbd1NI0wZigfIVOi-TKziF4_Crx3bm7zgSrJZqA,2403
onnxruntime/tools/mobile_helpers/coreml_supported_neuralnetwork_ops.md,sha256=JJBwv04C_o2K1UWOttDtarnC7xFFZQ2uvvIZg4EFmWA,1958
onnxruntime/tools/mobile_helpers/nnapi_supported_ops.md,sha256=uJznEyy7ZAdlrkKQeoWFFs55rPE-kOePIJiv741r98Q,2385
onnxruntime/tools/mobile_helpers/usability_checker.py,sha256=sEaT80-YwNwUxQbev3R2RzMzaaGR06slGdJuUpuSdRc,32378
onnxruntime/tools/offline_tuning.py,sha256=IswzPfMIbWwZKBBX4pat8zu4s1D5sUzVhLjDmWzQlkI,6368
onnxruntime/tools/onnx_model_utils.py,sha256=G9Wsgfiq46pgbkYTZAYupZKnqgZI-GT0RverFPn2sC4,16818
onnxruntime/tools/onnx_randomizer.py,sha256=9L96dzIf59cQ2oQsmR2EEsdrR4hHwEGrpZkajEgUPAY,3361
onnxruntime/tools/onnxruntime_test.py,sha256=SvqgwrjiIpf_vsZfHmkE_FPXJkDA18mZpwYoyjMv5g0,5770
onnxruntime/tools/optimize_onnx_model.py,sha256=DUm9R3qC6g1ZnXRdc7Mu7U03ArJF5j5oICJLh_amhXI,2015
onnxruntime/tools/ort_format_model/__init__.py,sha256=Xsq0LM-zEkNsULbTTvyxnz4dDzqepeE8BEgHzJGYOD8,1307
onnxruntime/tools/ort_format_model/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/operator_type_usage_processors.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/ort_model_processor.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/types.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/utils.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/operator_type_usage_processors.py,sha256=rl9Ub_QRC5B0F9Mu0LbY86bsVnkUf7XYn1PtwC1NPNM,27032
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgType.py,sha256=ErRXrmwza1xgVW4OAENw_B8yVc7WfWbAYYhiAO3Cc2g,147
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgTypeAndIndex.py,sha256=8Sx0mmUqjN4uN7MeTY6scIgeZO6gruARh8MvlEQdUdQ,2093
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Attribute.py,sha256=S5SA5y9FB2HWcPIPx99jz_ebTV85O8cTETJgGe_FKLo,11187
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/AttributeType.py,sha256=piF8u_U7zNE5eSrwcQhRLgvr2axK21-455McxIje2HQ,346
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Checkpoint.py,sha256=0cZ14luQy_w8GlbDF_xoGPWtee2UueQM4jt59YX5B9o,4342
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedKernelCreateInfos.py,sha256=K6Sl9lPxPE8huGE0tunG9MXDFYbzJs5Hc4nzKj8ft88,4648
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedNodeIndexAndKernelDefHash.py,sha256=Lr3cs1e80M1zLbZ1hBKwDsSl6d8zxjkbIfAguiTkJko,2526
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSessionState.py,sha256=N5CN72wJObCmYGcbA5z9o3u1khYxJ9bVSIaFIJCDR3E,3678
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSubGraphSessionState.py,sha256=ARjFKPJX_pB2KTrSUCHz_iN_UmrB3xRDKtUbIepTX-Q,2682
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Dimension.py,sha256=VHQcCV5ip0BIhm1EFtsAXA-uMp8tqlu1BfiJP2LZuQc,2262
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValue.py,sha256=14W1geQj5_V_5UiqgrG2oFGDgCkAIS5d71xemZwmO_Y,2574
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValueType.py,sha256=2kmuPePZe_rWSMkI7ceIhewHL3wvvsDEhzH-LBusm0M,174
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/EdgeEnd.py,sha256=aNdY1SA8fDyvH1Vsqo5xklVuhGtw2sUmyy86HmtWEbk,1137
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/FloatProperty.py,sha256=qqpRpfy9QXs7WHhHUARUjv5q_zs8wiT2-iOjKD9v4NQ,2075
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Graph.py,sha256=Y_AnjssgrJ7o4oU1w-KG-rOl8EE8YuEnvDWu7rGN57E,11039
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/InferenceSession.py,sha256=0FgbOyXfWicKshG131GzHqPTFJvoVorBxTVP1xM6ZNU,3125
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/IntProperty.py,sha256=EjWio-yFwfkE_N764bYTaDRz0EHSZ1n0JePkLSNGitk,2037
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrArgsEntry.py,sha256=lKy6VMEjvkiv5BGOjugWEZtlobNMwvlfcSRSYzjCiAk,3193
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrResolver.py,sha256=cufEs5uTENEF6bvZQemzDztbErNtvDuzMpDoCnSBpik,2867
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/MapType.py,sha256=wlC76nL8dC0vdZVlEoUC9qwgbVDwnSt5RCRO5r_Wfmg,2194
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Model.py,sha256=CPbe2q2OcKU8LbShHGtnIG_jrW83f3nvMR5Trg1Nx90,7663
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ModuleState.py,sha256=rGMagBBqyCNbewI-xUygxN07m7fLDQmH2EWISmFd7ss,4994
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Node.py,sha256=IjM4YjGq_swHTiLw01xsSWmAYaZmkVZUR3cei3byY68,10718
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeEdge.py,sha256=LiB9vyQTsFkfRRTuiDcwrRfVaWrRzAALP5Rop0rtGKg,4183
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeType.py,sha256=bBLbvDWziLLRxhjCAqlS44k_hdIWbWk3vsRxCDOLz7k,151
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodesToOptimizeIndices.py,sha256=KoWmACgRnK8qGNyXiKo6NywUvzpvYYxGQ0AyayjyaFQ,6144
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OpIdKernelTypeStrArgsEntry.py,sha256=6WV2Pj3vkqJ2NZ19VWrPAxKtoGCeiJ6NTVOvs89BIQ4,3387
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OperatorSetId.py,sha256=UOiU-CCvexY3OOWk2hiZyBI0OItcA6jiZTsERDZxu2U,2099
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OptimizerGroup.py,sha256=Xri5OFTQ881fzaVdQZjVviLyE5vH2aFGGArQE5H1ZbU,4135
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ParameterOptimizerState.py,sha256=AIEaCRcEln5wC7X3C8vp7TmmpxuaH7t93UCphxbxRJA,3218
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/PropertyBag.py,sha256=QXs064QNug8pG9zjX9aWEPmO63CIWJcL-kMLgMPk7vI,5099
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecord.py,sha256=Lrl5lCzbUL323Y1nnij-7vn95d3qNHkiVPwkbg6SSds,4067
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecordContainerEntry.py,sha256=PM7_R0kbnF3K32qoLOIPXmeudQfj0pvabMQLB-FjREw,3832
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizations.py,sha256=qYkC65wMqu7FinW2X9pPTeRD4bY7xdIsOfXnIJoFDFU,2800
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SequenceType.py,sha256=iiMOzyjzXzjO3zoad_oG2-ct786Wv8u4LWoFOZdmc0A,1829
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Shape.py,sha256=4Y6yU35JHSXKzb2BDzObkss4yiopwS3IXkSBpFf88FI,2352
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SparseTensor.py,sha256=3EZahzlyO_73WOzK9jZHwoVqsQkq3407ATBOG56KLEE,3806
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringProperty.py,sha256=Zd5QCcSgvf5ojuOBRV19gGyQPhqjJkoBaMdZ06kOc7c,2110
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringStringEntry.py,sha256=Iih7xRq4zvu4cVV4_TAC4sbjaH-dLqZGU68X0oLaWoI,2147
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Tensor.py,sha256=CZItxbQ-B9cJnaS5HwBNWT1buat_JgX-W-zOn9m11a4,6802
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorDataType.py,sha256=WmblM8GoLES62L12Ea3dC655_ruO2T3R2Q085OLCFc8,500
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorTypeAndShape.py,sha256=noUmY1JdkO-Bjhghyuevf7cy0Gokhnh0r_AcmIt1fl4,2326
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfo.py,sha256=ToFYZzH0Vit5VVIztodfzP-fCOc97vgBHXz6Y_tHyWk,2599
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfoValue.py,sha256=a2VKzgcR6v4qiffa5a5HYNUaHKDLdmsFbAdsNwrmT5Q,198
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ValueInfo.py,sha256=ZC6peiqYRYKYw_si4BmtZUbRRLf9BkkD4za4xky3QHY,2655
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__init__.py,sha256=EfkIrreUF6TrcYpBo1NJ8GOV_p_o_YXg3fSptBN5XUo,251
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ArgType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ArgTypeAndIndex.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Attribute.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/AttributeType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Checkpoint.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedKernelCreateInfos.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedNodeIndexAndKernelDefHash.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedSessionState.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedSubGraphSessionState.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Dimension.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DimensionValue.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DimensionValueType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/EdgeEnd.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/FloatProperty.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Graph.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/InferenceSession.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/IntProperty.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/KernelTypeStrArgsEntry.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/KernelTypeStrResolver.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/MapType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Model.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ModuleState.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Node.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodeEdge.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodeType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodesToOptimizeIndices.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OpIdKernelTypeStrArgsEntry.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OperatorSetId.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OptimizerGroup.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ParameterOptimizerState.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/PropertyBag.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizationRecord.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizationRecordContainerEntry.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizations.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/SequenceType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Shape.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/SparseTensor.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/StringProperty.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/StringStringEntry.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Tensor.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TensorDataType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TensorTypeAndShape.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TypeInfo.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TypeInfoValue.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ValueInfo.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_model_processor.py,sha256=Zh07RmcGYf0-qbXRiim2DWUGHbsrEeG6_rOcx3VLexw,4472
onnxruntime/tools/ort_format_model/types.py,sha256=8r38Y7ghaGn6_4muEWSpeDHkY3rsIwAB76t5YD8uMFk,4468
onnxruntime/tools/ort_format_model/utils.py,sha256=rURCBd9_214ScVY9dkHF3xWbmh3hAGbeDPHXJVlMmmk,2595
onnxruntime/tools/pytorch_export_contrib_ops.py,sha256=DypErhw_nTIDpINn2omBl4Wb-tmvLzEtUjkl_Krgo3I,4909
onnxruntime/tools/pytorch_export_helpers.py,sha256=MRegHn3z3VhVbZQ4O-kTGedIE-pufyxhq1A1GVIdCjY,5971
onnxruntime/tools/qdq_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/qdq_helpers/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/qdq_helpers/__pycache__/optimize_qdq_model.cpython-311.pyc,,
onnxruntime/tools/qdq_helpers/optimize_qdq_model.py,sha256=9kpU0Dukc0ZEHnjwO7NqNFUFVRtOx8xgFPVWZpXkEcQ,1279
onnxruntime/tools/qnn/__pycache__/add_trans_cast.cpython-311.pyc,,
onnxruntime/tools/qnn/__pycache__/gen_qnn_ctx_onnx_model.cpython-311.pyc,,
onnxruntime/tools/qnn/__pycache__/preprocess.cpython-311.pyc,,
onnxruntime/tools/qnn/add_trans_cast.py,sha256=0NY0qI3b_8LGlJcXOZzfsHyZ48GSKyx0TOErVdDl6P8,13748
onnxruntime/tools/qnn/gen_qnn_ctx_onnx_model.py,sha256=mDumDp_8zChijEitCcfY7kWxr5uBqC2evKQlVV4P_cs,16562
onnxruntime/tools/qnn/preprocess.py,sha256=ubAAXztNQSVVBByQazXWKRt-1xzmq3EWAb1rRHkgdbg,7440
onnxruntime/tools/reduced_build_config_parser.py,sha256=F9bN5_yi5V7nLQUS2vsfo7gbYB1cZnSAQiUTZOdvqM8,10161
onnxruntime/tools/remove_initializer_from_input.py,sha256=C1vDZZBek-TK6SV1q6e_7kqkyQzS8iqHE8GzZgehhiE,1031
onnxruntime/tools/symbolic_shape_infer.py,sha256=Ya-aBHWAzZ5AHh75UE9mhhp3_3HiONkdPExSeWtu3XY,145673
onnxruntime/tools/update_onnx_opset.py,sha256=fplb1ypV-pFhu8Xsi5u_bDfI7EsC4zamJkTziccgQ2c,1182
onnxruntime/transformers/__init__.py,sha256=2c213CqzXrc0N6Cqf__Te5d_SH_stfLdNdeNrugB7SQ,321
onnxruntime/transformers/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/affinity_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/benchmark.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/benchmark_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/bert_perf_test.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/bert_test_data.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/compare_bert_results.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/constants.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/convert_generation.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/convert_tf_models_to_pytorch.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/convert_to_packing_mode.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/dynamo_onnx_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/float16.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_clip.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_sam2.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_unet.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_vae.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_bart_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_base.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_bias_add.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_biasgelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_biassplitgelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_conformer_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_constant_fold.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_embedlayer.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_fastgelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gelu_approximation.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gemmfastgelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gpt_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gpt_attention_megatron.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gpt_attention_no_past.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_group_norm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_layernorm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_mha_mmdit.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_nhwc_conv.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_options.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_gelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_layernorm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_matmul.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_quickgelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_reshape.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_rotary_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_shape.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_simplified_layernorm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_skip_group_norm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_skiplayernorm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_transpose.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_utils.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/huggingface_models.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/import_utils.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/io_binding_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/large_model_exporter.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/machine_info.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/metrics.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_exporter.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bart.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bert.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bert_keras.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bert_tf.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_clip.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_conformer.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_gpt2.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_mmdit.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_phi.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_sam2.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_t5.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_tnlr.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_unet.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_vae.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_utils.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/optimizer.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/past_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/profile_result_processor.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/profiler.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/quantize_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/shape_infer_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/shape_optimizer.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/torch_onnx_export_helper.cpython-311.pyc,,
onnxruntime/transformers/affinity_helper.py,sha256=KOKBvzoBr-wOk0QHMGKzY1uy1iI7E6eHpYwBdTHM-Y4,1442
onnxruntime/transformers/benchmark.py,sha256=vv5N75VZUdz65ZiXxk6y2bU5VjnBlCzNa5K0AYG_DKM,33784
onnxruntime/transformers/benchmark_helper.py,sha256=iJYVaNbevxQ0K3VC7GlvzH2HrmvY4q9uRumV-9LTS0U,23211
onnxruntime/transformers/bert_perf_test.py,sha256=AZmr21LQ22dcbJjOklfXGdwKn-nhDzZgHalzDDyGNFA,21129
onnxruntime/transformers/bert_test_data.py,sha256=8AFHdTtsKqaOARwRmXhQV59fBwatohpFeHVSeANn4kY,23446
onnxruntime/transformers/compare_bert_results.py,sha256=WzGCIGUwgOWzxesaWKGii0Xy3JfcY8LFkr8qceRR2vg,8448
onnxruntime/transformers/constants.py,sha256=HBmbfaRLrLCen1q0C9tZYJApn2NBPX2p2h05hc5DTsE,1127
onnxruntime/transformers/convert_generation.py,sha256=04WN23AxUEcaWpq-CaC5CBlxrbKWSItiBCFpV893EtI,145070
onnxruntime/transformers/convert_tf_models_to_pytorch.py,sha256=DMOWS-tr3BlcEhk_35LyHfC6E15s7gsVWkb_HLaVzQU,6841
onnxruntime/transformers/convert_to_packing_mode.py,sha256=2HlOMlu-PXx5Y-JPJaWsU4na7UcA9bqvKR-3-IPGjks,16792
onnxruntime/transformers/dynamo_onnx_helper.py,sha256=88r04Lcz88tZM9xFGkdbyMyc5tIWD1VoyYl180sgwXA,7802
onnxruntime/transformers/float16.py,sha256=nMTYghlnKLfqnnAVnsUYCKEwt_nkcPe3T9JIfg4Un3A,24687
onnxruntime/transformers/fusion_attention.py,sha256=VVYBo0ZegDgzXMlgiAXU_rgpNWDnOsS5ACU-M0DsRwo,51393
onnxruntime/transformers/fusion_attention_clip.py,sha256=NJpa0eJLD_a_tTKsXCSfGmnOs0pNggQ0tIqNGnvLzjE,14145
onnxruntime/transformers/fusion_attention_sam2.py,sha256=s5TNIr5hi8UiorlfAzEAlJKUfcn6WhEvw5CEG1bH3Pg,21307
onnxruntime/transformers/fusion_attention_unet.py,sha256=CAQikTpvz4KbCJUx9jonZzgy30l_w9ul6hW1RNnXUtw,56955
onnxruntime/transformers/fusion_attention_vae.py,sha256=1JwY-DnsngQymXCIhb_huu3SzvgXcuKDkfC7duSlfgY,12379
onnxruntime/transformers/fusion_bart_attention.py,sha256=EcTHIMM-pUIQ1expZ0eCtpWHMq59uZkdqq9ANjy2JGQ,20277
onnxruntime/transformers/fusion_base.py,sha256=TOduoZTiSfrh-2QyM7F7YWb1HpJnA9uOGY0IwHFl1yo,5978
onnxruntime/transformers/fusion_bias_add.py,sha256=YOOZSfui3NOV0GUcNJmRSz-hoZOjQnRimjfAZPNOTzM,2041
onnxruntime/transformers/fusion_biasgelu.py,sha256=vGamxthOu6jXsxCRVdTFaP25-_tnjz9TVq91pIRV_Is,2300
onnxruntime/transformers/fusion_biassplitgelu.py,sha256=qN_YFjwT8jmBUaDobT9eGRZDCT6vSlbJvD0ZStgNFJs,4491
onnxruntime/transformers/fusion_conformer_attention.py,sha256=G8fq5BVB6t8--8xCpr1_tKmD6Vf1kw9UncCKB7CxkKk,8424
onnxruntime/transformers/fusion_constant_fold.py,sha256=qkthyYCZlSl1RMvfncCCmfkDzCsZ4tlOE9kf1-qENOA,6014
onnxruntime/transformers/fusion_embedlayer.py,sha256=RxQ2ZhT96d2g7-m9QcRAj4mD63Dn2s1Zz2J59AykKp8,36674
onnxruntime/transformers/fusion_fastgelu.py,sha256=RwvKIBJGRMk2MoH9j6HtFfQByH_Hw6eGmjeX9dtx_Lg,18181
onnxruntime/transformers/fusion_gelu.py,sha256=S9Jia08I3jXwafbo73xrqPOCT7rNHCnpASjZLHWiRcU,10451
onnxruntime/transformers/fusion_gelu_approximation.py,sha256=Xsa2v5mHjEuZrwnf1bm3UCCJ8I1is0dmuzzXgf5zDl4,1029
onnxruntime/transformers/fusion_gemmfastgelu.py,sha256=iRdK6jX-LlUIBGxjGavJsZKgLrj-hPBXpLvA13UIoR4,4208
onnxruntime/transformers/fusion_gpt_attention.py,sha256=20ZhplkAVJ3rq1VWwcNRmRs6OZu7lTHKIop3SAyDSUw,22508
onnxruntime/transformers/fusion_gpt_attention_megatron.py,sha256=HhoweTBxleb1niPOU_cfQzvUwM4LjxCVuZZWVEy3Imw,13639
onnxruntime/transformers/fusion_gpt_attention_no_past.py,sha256=qQb8WekiDJeQUV8egoCTrLoZki018veZTVVE-w3p3ds,10794
onnxruntime/transformers/fusion_group_norm.py,sha256=NBilP_TOZjlhuUS81EEEZMDNDzRPvje_r4_VTx9YlJ4,7645
onnxruntime/transformers/fusion_layernorm.py,sha256=qWb5Zp7itAXFyGeH4yBRr_XSvMZt1iTukBi_uzWWReU,20845
onnxruntime/transformers/fusion_mha_mmdit.py,sha256=l6KDhrKj0-9Q-t7hCYcWxzzkUUuPu6HFUPaPJZ_GwT4,25816
onnxruntime/transformers/fusion_nhwc_conv.py,sha256=Se5emPntX7hA0q20yklwu5RSyn_6D_vpUHMo4Z5U5HE,3948
onnxruntime/transformers/fusion_options.py,sha256=NSeQVc9Hz9S-4uGpxuYfsigzdLDOdsRqXyujbfRPGhw,12704
onnxruntime/transformers/fusion_qordered_attention.py,sha256=s1BQ8OolahrgxaSriVmli2AqTmn_fNQZH2KxU1HhmRM,17137
onnxruntime/transformers/fusion_qordered_gelu.py,sha256=4nuNJ0W9mDKH9Uw3tuEOc8O_ld2iRLA-8t0CUaPx7IE,4410
onnxruntime/transformers/fusion_qordered_layernorm.py,sha256=hzWVzxKVlL_fYd4Nv-xlOb9AYSQjHY_j3HzkFv-p_XI,4932
onnxruntime/transformers/fusion_qordered_matmul.py,sha256=5mjdEPTb8aT_ajAZFqEynM6fXWjJmg4iBpz3MrR-ayY,8541
onnxruntime/transformers/fusion_quickgelu.py,sha256=e7MMq9G2aiIotoxnkd0a7-XmvyY-wYyefdTmXtBO_f8,2869
onnxruntime/transformers/fusion_reshape.py,sha256=AfT88v22G6PgZPzVKM39_QduUnlIbe8dbxvPCh-5dkg,6403
onnxruntime/transformers/fusion_rotary_attention.py,sha256=W0qhTvG7_AZdfpUZsujGYBY7KVJenqgQ5n46Iz-zeh8,68222
onnxruntime/transformers/fusion_shape.py,sha256=SbwDCkucLt3U-kCdxEEaJAJa4nWRtKdU81qHYYcytfk,3763
onnxruntime/transformers/fusion_simplified_layernorm.py,sha256=RovL8O0nVTMZTWgvXQ-vfzH_1k9fd0hDY70oKAu0Ly4,7831
onnxruntime/transformers/fusion_skip_group_norm.py,sha256=_dK_FotisHnc4s0zuyAzYNjHBznh_0iGWHuSD6BgGzo,10855
onnxruntime/transformers/fusion_skiplayernorm.py,sha256=P4r7cXVTjRLZIyOqkOXl5gUjiOBiXRQaaV7u4Xm__I0,9168
onnxruntime/transformers/fusion_transpose.py,sha256=OkcS-03fQuIau8MmSXxuXyUIZqlfp7xYkE_DyuzOneM,7004
onnxruntime/transformers/fusion_utils.py,sha256=rEMNm7hHFf3t3ZAS9dsQ2q3Z0QYtztao7U0GH3HEY5k,13177
onnxruntime/transformers/huggingface_models.py,sha256=9X08Ad57xm-GiPcB6YcHRJT10QBE-DmGUL32xkP3OIU,4005
onnxruntime/transformers/import_utils.py,sha256=_ILscQRcSyaJHt1l6jqcny5FWy7Qr6N_7hKs5aav8oM,651
onnxruntime/transformers/io_binding_helper.py,sha256=fFO1ZICge3qwNgf0gPAh_ppd5MYYj-cwAfYeHA8b3os,17601
onnxruntime/transformers/large_model_exporter.py,sha256=f9VswBG1yIu_z6I4OfU6rYefIA2zladP9INzeo2Z-c8,15296
onnxruntime/transformers/machine_info.py,sha256=6BwCxyjqbI0NdIo6xdC2Co2fbrIjv0BBvnEtmbLP9e4,7521
onnxruntime/transformers/metrics.py,sha256=DEjoDQGAf2Yq_BAz66x3w0KZm1QhvG4ZENk8rIIC2UQ,5223
onnxruntime/transformers/models/bart/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/bart/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/bart/__pycache__/export.cpython-311.pyc,,
onnxruntime/transformers/models/bart/export.py,sha256=PNlhkbvrxTxSSLXpzqoa02Lektzf8rdZpcVFBxw-qcI,4285
onnxruntime/transformers/models/bert/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/bert/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/bert/__pycache__/eval_squad.cpython-311.pyc,,
onnxruntime/transformers/models/bert/eval_squad.py,sha256=If_RsXJHUt0O78-FLnfbS4DXeccihgm7HFdhC8NzGMA,12355
onnxruntime/transformers/models/gpt2/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/gpt2/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/benchmark_gpt2.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/gpt2_helper.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/gpt2_parity.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/gpt2_tester.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/parity_check_helper.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/benchmark_gpt2.py,sha256=53krYHNeJBk8ZixWoWrZHxCbe9z4bDYiGW02tzxR-88,15930
onnxruntime/transformers/models/gpt2/convert_to_onnx.py,sha256=pqz7enfiU8ZXxbghKekL2H-ItDb_WW3rSu52K8FGTEk,20618
onnxruntime/transformers/models/gpt2/gpt2_helper.py,sha256=1mZeKeDKm5jXgVg2sE5Jofbyg_0HQ84d9Gqg26dLPes,41331
onnxruntime/transformers/models/gpt2/gpt2_parity.py,sha256=A963JoK1zQb6UgFUOp1strqjoJ_lZQ2pvrFLa0UjIMA,18248
onnxruntime/transformers/models/gpt2/gpt2_tester.py,sha256=JqQKJvOfHKcNcnwtDIp_k98YHuT9BSmVfz2th8IIY-c,20071
onnxruntime/transformers/models/gpt2/parity_check_helper.py,sha256=x54knOO4LgzWvle3N3HuWyRTTNhhcanwMa12AredDTo,5840
onnxruntime/transformers/models/llama/__init__.py,sha256=yR2FucNw-jt_3CbNt-zuM7DmldPq1rJK3SV8gRISzN0,490
onnxruntime/transformers/models/llama/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/benchmark.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/benchmark_all.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/benchmark_e2e.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/dist_settings.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/llama_inputs.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/llama_parity.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/llama_torch.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/quant_kv_dataloader.cpython-311.pyc,,
onnxruntime/transformers/models/llama/benchmark.py,sha256=NI08C5ghVJQenbYw8sEUJ7L8snbNB9_6aGdsyttjLYs,27488
onnxruntime/transformers/models/llama/benchmark_all.py,sha256=_KWRWk3Xbtzg7HOjU3sKGutAd1jDXDfal0wnUcD2_TU,15801
onnxruntime/transformers/models/llama/benchmark_e2e.py,sha256=741bV8ZSqA0CmP5quL3gMuIXYp3hwBoihPrsqPEI2Po,25477
onnxruntime/transformers/models/llama/convert_to_onnx.py,sha256=Qb2fJmy3s3EY1HfA6p9KXlJKfvFdyZPxYp6BZ8Efm88,42942
onnxruntime/transformers/models/llama/dist_settings.py,sha256=swRa5c26FmnPJloTRqELnK0OrYmE1mI_RrM9yf_Q5ho,1659
onnxruntime/transformers/models/llama/llama_inputs.py,sha256=x7vxfIAMA0QmMVtjDVNSOoA8nrKv22bTnRNQfSyK1YY,20726
onnxruntime/transformers/models/llama/llama_parity.py,sha256=yqohE-LwOowSfI8btjrCZv3Z67oVAVsXXP63kqUWqak,11908
onnxruntime/transformers/models/llama/llama_torch.py,sha256=OIbSpg7bjfPN9SzyGfaBKmzsa-bGHQTx0X-etiEl0Zk,1732
onnxruntime/transformers/models/llama/quant_kv_dataloader.py,sha256=piVldpGm9eBmF4wzgmKJprhujqTPddqORxZyLizcJdA,4959
onnxruntime/transformers/models/longformer/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/longformer/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/benchmark_longformer.cpython-311.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/generate_test_data.cpython-311.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/longformer_helper.cpython-311.pyc,,
onnxruntime/transformers/models/longformer/benchmark_longformer.py,sha256=V37e3E4jv1hFYca4hCYtYHW3n_n6_CXx6AlsGeePNFU,30242
onnxruntime/transformers/models/longformer/convert_to_onnx.py,sha256=cTmSpSZhytBrM40Ys1r4FCUctyovXS3_e40_iozD4Bk,15219
onnxruntime/transformers/models/longformer/generate_test_data.py,sha256=wQxpgo_vZBhKRlquJwUB9FH3_xxvyDC3aCCZdkvADLM,9964
onnxruntime/transformers/models/longformer/longformer_helper.py,sha256=n3crEYnVYZxNzoyTNMzX3LFgxHH-FcaqtdfRmyAMg-4,3123
onnxruntime/transformers/models/phi2/__init__.py,sha256=yR2FucNw-jt_3CbNt-zuM7DmldPq1rJK3SV8gRISzN0,490
onnxruntime/transformers/models/phi2/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/phi2/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/phi2/__pycache__/inference_example.cpython-311.pyc,,
onnxruntime/transformers/models/phi2/convert_to_onnx.py,sha256=D2BY8_oidJhj6yLKvZfojioI6nA6WvPlfAeR0Rn4NU8,20742
onnxruntime/transformers/models/phi2/inference_example.py,sha256=4Q7VjNGCvrjIbC1Ul8W8SRndaiI42cGQ1L4iO0vMwlg,17705
onnxruntime/transformers/models/sam2/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/sam2/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/benchmark_sam2.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/image_decoder.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/image_encoder.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/mask_decoder.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/nvtx_helper.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/prompt_encoder.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/sam2_demo.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/sam2_image_onnx_predictor.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/sam2_utils.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/benchmark_sam2.py,sha256=2ABs0vz1B5bN0EaRUUMstvmez4hjt15teLK3PdqECcg,22468
onnxruntime/transformers/models/sam2/convert_to_onnx.py,sha256=g1y2o679JtjTEkCHJn3r-QSvosIBKBTeUz6mGTHctnI,10669
onnxruntime/transformers/models/sam2/image_decoder.py,sha256=XCxCgIXSKLKvO4CQXGuyEuoXeDn9yxy8RnmfQgALOWc,11093
onnxruntime/transformers/models/sam2/image_encoder.py,sha256=AEFh2pdqwmJkdKVsowms0Va8w8-iKhsRk5Hu5eajWyQ,9788
onnxruntime/transformers/models/sam2/mask_decoder.py,sha256=QwnZgjWwFsLdoNmnr7OG8Kgk5lrpZfHOeFGg5LLXQG0,9066
onnxruntime/transformers/models/sam2/nvtx_helper.py,sha256=XJFujDb-p27zCJs-O-ZjcKEAetc5dc0keFvsIYgFQ6E,1312
onnxruntime/transformers/models/sam2/prompt_encoder.py,sha256=STPG6PNZ5oVmuNdfeWuHkxxR2cOiwXN4lYKnvm3pyNA,8530
onnxruntime/transformers/models/sam2/sam2_demo.py,sha256=SH522SETO-RHeqH2Em848TTqe2iBXXYgp5iDc-xPA-0,10822
onnxruntime/transformers/models/sam2/sam2_image_onnx_predictor.py,sha256=bB-f7bi7NSb0XVeAA6J3v9jkMMJDHdHmS5PdX1UO0nU,12702
onnxruntime/transformers/models/sam2/sam2_utils.py,sha256=gNzqfMwdmIbkWCvRrKeJ1uYpz9fqFxrGNGQ9ikMxRfc,5667
onnxruntime/transformers/models/stable_diffusion/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/stable_diffusion/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/benchmark.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/benchmark_controlnet.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_txt2img.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_txt2img_xl.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_utils.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/diffusion_models.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/diffusion_schedulers.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_ort_cuda.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_ort_trt.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_tensorrt.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_torch.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/optimize_pipeline.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/ort_optimizer.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/pipeline_stable_diffusion.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/trt_utilities.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/benchmark.py,sha256=juQU_HjW-9TKbquKxae2Q-7lcj1FGparBQ3aqPhFY3I,51696
onnxruntime/transformers/models/stable_diffusion/benchmark_controlnet.py,sha256=hd1yCasPxxcu7g9O2Bg-CRW4IsPM-PE8BbNze2TdtW0,13355
onnxruntime/transformers/models/stable_diffusion/demo_txt2img.py,sha256=k_H66rQBSryX5MvHzheAcKq5pXIMPeoktzxP-EKfrPw,3394
onnxruntime/transformers/models/stable_diffusion/demo_txt2img_xl.py,sha256=v_DYURWp1UdXHSY6ppmt4DU56PJsXJm4ILyMz4Fif_g,10196
onnxruntime/transformers/models/stable_diffusion/demo_utils.py,sha256=nz5QzXHy12J5VoSvt-lKxLt4c_VFf58K0OI3hoiZP1A,29376
onnxruntime/transformers/models/stable_diffusion/diffusion_models.py,sha256=SwAEjw6mI08qNxpr6f8xfeg_TrhafUeLmSSkfePWuUs,51716
onnxruntime/transformers/models/stable_diffusion/diffusion_schedulers.py,sha256=ohAEuJNU_-rgz9wwz7pRc7tJI1QJOc5zjJYkvuh2jKs,49468
onnxruntime/transformers/models/stable_diffusion/engine_builder.py,sha256=YFe6_0DI97qhfdlkyliqXCBnobWrTVWfJl2uL-HaAXs,11965
onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_cuda.py,sha256=t9cjyv617MpQt_S7crnspAHbw0WT8Rf3lCVRkeLwuVo,16241
onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_trt.py,sha256=0M-JLT3Z1zYPEVkJ0TPCZuhbIFCstbBi5Wh623VLcww,11451
onnxruntime/transformers/models/stable_diffusion/engine_builder_tensorrt.py,sha256=-VCFZgeT233jWR1B8mzSJWEpoPOUbHNWTnWVMXmf0k4,16016
onnxruntime/transformers/models/stable_diffusion/engine_builder_torch.py,sha256=I_viNNv16MEMklC6yVByLa3fIKwdp2iSLqwCCflQK3M,4306
onnxruntime/transformers/models/stable_diffusion/optimize_pipeline.py,sha256=Cpvy-AITaTEqAssovg3ReHOzHc_UCdn3D7rPPEYV5OM,22004
onnxruntime/transformers/models/stable_diffusion/ort_optimizer.py,sha256=Yl8skQoXYbouhw5ti2zCOfbMLbSm3iXniA5GLmQ9A3s,5853
onnxruntime/transformers/models/stable_diffusion/pipeline_stable_diffusion.py,sha256=EDU3Hnvo4nX3u4VRxw5uyHQZC59u-RiXrIxMhzGPvPg,34029
onnxruntime/transformers/models/stable_diffusion/trt_utilities.py,sha256=XZCfqG_kZ72e-L9p7PlGqc4NLvFZF1h40A6Guyj6z8k,432
onnxruntime/transformers/models/t5/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/t5/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_decoder.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_encoder.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_encoder_decoder_init.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_helper.cpython-311.pyc,,
onnxruntime/transformers/models/t5/convert_to_onnx.py,sha256=70tYNOGfx0HgIHgMdbRTLbsJlhQkEbW4h2xZveSvNeE,10510
onnxruntime/transformers/models/t5/t5_decoder.py,sha256=GmUTeBavhX2TfIjZIfMNuMML_3-6BRqetUEXAvOxFPo,17188
onnxruntime/transformers/models/t5/t5_encoder.py,sha256=xSjy9AmscMdyCDzSn-v59xXqS634lAs1EmsJUsBaB0E,2319
onnxruntime/transformers/models/t5/t5_encoder_decoder_init.py,sha256=hf7ozFvgXSE_FH17p88MS0fpPBbnlsUYfKgncCkmnvU,15421
onnxruntime/transformers/models/t5/t5_helper.py,sha256=FKwaOB-z4uvEBpsKZbJgVjMP6xl2D9fryfHjD-4eneA,12515
onnxruntime/transformers/models/whisper/__init__.py,sha256=yR2FucNw-jt_3CbNt-zuM7DmldPq1rJK3SV8gRISzN0,490
onnxruntime/transformers/models/whisper/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/benchmark.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/benchmark_all.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_chain.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_decoder.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_encoder.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_encoder_decoder_init.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_helper.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_inputs.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_jump_times.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/benchmark.py,sha256=Qy4NcHoo5KG_QVmFl2V8_h-F-P2qawlqMOe3HEk33M0,23356
onnxruntime/transformers/models/whisper/benchmark_all.py,sha256=rO0XLk4u6_Gqe1gFm5oZ95o_k68BJjYVo5NrY7Vz8r0,19368
onnxruntime/transformers/models/whisper/convert_to_onnx.py,sha256=MiWO26bFYVAbcd2IJWmyXlMq5ccsp3x-4bIhFgIQlII,20201
onnxruntime/transformers/models/whisper/whisper_chain.py,sha256=vCPpFzghlLsyMPBwemLh6zWSE1qO9EfbozZH5ywrgJg,15230
onnxruntime/transformers/models/whisper/whisper_decoder.py,sha256=za4PBjUSbx-lwQReCMs7ODBK19kUVvROwGY2_01AyRk,21862
onnxruntime/transformers/models/whisper/whisper_encoder.py,sha256=vyykcthAejmd2K4lJyqd3JxXxTwcOVhxqMdj7AAeyUU,6342
onnxruntime/transformers/models/whisper/whisper_encoder_decoder_init.py,sha256=PoseFY-kxybyiCd0JVhPT1bC9rG_qzKmY76mKl37eGk,16802
onnxruntime/transformers/models/whisper/whisper_helper.py,sha256=ifEhlnK5NkGUlJrJNECp1nArm3FInaVa7mXTOuogZEQ,51347
onnxruntime/transformers/models/whisper/whisper_inputs.py,sha256=rSjFJ9Llz2FmpRXDz8cK2KFzr2b5l-n4eo8VN463iUE,16041
onnxruntime/transformers/models/whisper/whisper_jump_times.py,sha256=bgxGRs00NAf5F7hzJBJbt7JB3IMuCOxylhCgnf16-l8,19950
onnxruntime/transformers/onnx_exporter.py,sha256=jFFFq0gGDtDtLgi0zoDZuIUR3ba_r6J-a2b7IgoRAnQ,25340
onnxruntime/transformers/onnx_model.py,sha256=tg3iprgpcEFwnRk4ADiXrKxD-Sj-kI4-IeEYEDOiCko,69954
onnxruntime/transformers/onnx_model_bart.py,sha256=yLPRW-4YuZ_H_6xN5H4hE2Wr1bYp7Em4ZrP_T0hC20s,5547
onnxruntime/transformers/onnx_model_bert.py,sha256=4UF9Omcq2cKQ4VeycaH9olH8h4RvlEQUsrkuR3qXG3Q,20402
onnxruntime/transformers/onnx_model_bert_keras.py,sha256=iAlnQJJCZMGBUx5fLErc7FInVzBcz14ncc-RAO3svqU,19010
onnxruntime/transformers/onnx_model_bert_tf.py,sha256=Ebq6LeUyxIzE8_LG_xvaPRxtRIlD2gTtVKE5UyeGoRo,25503
onnxruntime/transformers/onnx_model_clip.py,sha256=RyBZh4nOvV7TP_b_SN_nSNgK5noVhtwOHbKhiTCifds,1394
onnxruntime/transformers/onnx_model_conformer.py,sha256=lP4r-iid7Jl1AAOGdZM5S_MCQ__oRq-KTibGcM6Gri4,1412
onnxruntime/transformers/onnx_model_gpt2.py,sha256=3LmzgHuLvO5tyNHKWGidttyqrcpIE7aLBYbRqzjolUg,3913
onnxruntime/transformers/onnx_model_mmdit.py,sha256=gyQ7AuF9ZU6o6oOEytM4HMo-B5p6JvXPxM5xGAdjEpk,4209
onnxruntime/transformers/onnx_model_phi.py,sha256=lChW-NHkl5YWRhwyMCpwMYDSfXFdYmIx456SB3TQbKQ,36339
onnxruntime/transformers/onnx_model_sam2.py,sha256=3mojj1dGPaw8gb8jJABQzw9rLHD1IBdpVEM-h-Tpt6U,4981
onnxruntime/transformers/onnx_model_t5.py,sha256=-cl2iOALXNMff_zFsi9eCiwIJKWHFDmjVnfQ8cIQTxo,38056
onnxruntime/transformers/onnx_model_tnlr.py,sha256=6MlEn9yRlF3jVW1zd9NCR0qxnPgIa2dEhaylvbf8dCo,8405
onnxruntime/transformers/onnx_model_unet.py,sha256=OsQqUi3Ak5eMK2w2-j294o9bwchH0zH0gYgmEZUhLRQ,9513
onnxruntime/transformers/onnx_model_vae.py,sha256=JTp3ctfgHR7YERn9cNYpXB4ZM82X3CgTYHtVdgrJqxY,1513
onnxruntime/transformers/onnx_utils.py,sha256=GGkO0ebsoPvVOPNfeYC0tN9DJWiBaLXXzjQhBrAXvNE,2175
onnxruntime/transformers/optimizer.py,sha256=gGDTlZnTeYINKxmBJ_98fF6pdL-LSeCotT57-iskmzw,25859
onnxruntime/transformers/past_helper.py,sha256=uDRilngPAzWQ-ZCNiOivB_qQUroOSSZu6A2EZg6mu0o,6955
onnxruntime/transformers/profile_result_processor.py,sha256=fZSWV2p3QWkG2cgqfEt1dC9rglnY8UgviCWs6W4agek,12940
onnxruntime/transformers/profiler.py,sha256=fp42DHIBu4VZMDJTVclEm83TEh-HNwPQGz0_AvtLNmg,13736
onnxruntime/transformers/quantize_helper.py,sha256=wcptMQ4RiSZ18DhhPFAZ8AbKrbNx--UGj63S6lBVOig,2927
onnxruntime/transformers/shape_infer_helper.py,sha256=v7VKU2m7blQp21PBbDPeySgwFMoP_BC1OM_rUIlnAFo,4566
onnxruntime/transformers/shape_optimizer.py,sha256=McbgtLsmb3j0AVHWnVigktDgxMktE0UXAdyemg3NNjU,15468
onnxruntime/transformers/torch_onnx_export_helper.py,sha256=DOTqWF9DEbxsxqKWtq3NCqcA7de-JSMgjS-MyczJimg,2575
onnxruntime_directml-1.23.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
onnxruntime_directml-1.23.0.dist-info/METADATA,sha256=ynwQxJ_2hh7lrvzmnC8Ac4CuUcsDfMW5pe5_8yNVbOY,4853
onnxruntime_directml-1.23.0.dist-info/RECORD,,
onnxruntime_directml-1.23.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime_directml-1.23.0.dist-info/WHEEL,sha256=y4n9_669c4ZQLyT56MHjc_JUbnwtaZfMVMycweN557o,102
onnxruntime_directml-1.23.0.dist-info/entry_points.txt,sha256=7qLS4FbGXwPZjfdpVAGpnmk9I6m6H5CxEnwcCx1Imjs,77
onnxruntime_directml-1.23.0.dist-info/top_level.txt,sha256=zk_fJEekrTm9DLxX2LwGegokVqP6blqPhFoMIuh0Nv8,12
