#!/usr/bin/env python3
"""
Technical Implementation Test Suite
Tests all components described in AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md
Validates display detection, screen capture, input methods, and system integration
"""

import sys
import time
import traceback
from pathlib import Path
from termcolor import colored

# Add lib directory to path
sys.path.insert(0, str(Path(__file__).parent / "lib"))

from aimbot import Logger
from display_mode_detector import DisplayModeDetector, DisplayMode
from enhanced_screen_capture import EnhancedScreenCapture, CaptureMethod
from input_method_manager import InputMethodManager, InputMethod, InputConfig
from system_integration_manager import SystemIntegrationManager, SystemConfiguration
from performance_optimizer import PerformanceOptimizer


class TechnicalImplementationTester:
    """Comprehensive test suite for technical implementations"""
    
    def __init__(self):
        self.logger = Logger("TechnicalTester")
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        
    def run_all_tests(self):
        """Run all technical implementation tests"""
        print(colored("=" * 80, "cyan"))
        print(colored("TECHNICAL IMPLEMENTATION TEST SUITE", "cyan"))
        print(colored("Testing components from AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md", "cyan"))
        print(colored("=" * 80, "cyan"))
        
        # Test individual components
        self.test_display_mode_detector()
        self.test_enhanced_screen_capture()
        self.test_input_method_manager()
        self.test_performance_optimizer()
        self.test_system_integration_manager()
        
        # Show final results
        self.show_test_summary()
    
    def test_display_mode_detector(self):
        """Test display mode detection functionality"""
        print(colored("\n🖥️  Testing Display Mode Detector", "yellow"))
        
        try:
            detector = DisplayModeDetector(self.logger)
            self.record_test("Display Mode Detector - Initialization", True)
            
            # Test display mode detection
            display_info = detector.detect_foreground_display_mode()
            self.record_test("Display Mode Detector - Detection", display_info is not None)
            
            if display_info:
                print(f"   Detected Mode: {display_info.mode.value}")
                print(f"   Capture Compatible: {display_info.is_capture_compatible}")
                print(f"   Performance Impact: {display_info.performance_impact}")
                
                # Test compatibility report generation
                report = detector.get_capture_compatibility_report(display_info)
                self.record_test("Display Mode Detector - Report Generation", len(report) > 0)
                
                # Test specific mode detection logic
                if display_info.mode == DisplayMode.FULLSCREEN_EXCLUSIVE:
                    print(colored("   ⚠️  WARNING: Fullscreen exclusive mode detected!", "red"))
                elif display_info.mode == DisplayMode.BORDERLESS_WINDOWED:
                    print(colored("   ✅ OPTIMAL: Borderless windowed mode detected!", "green"))
                
        except Exception as e:
            self.logger.error(f"Display mode detector test failed: {e}")
            self.record_test("Display Mode Detector - Overall", False)
    
    def test_enhanced_screen_capture(self):
        """Test enhanced screen capture functionality"""
        print(colored("\n📸 Testing Enhanced Screen Capture", "yellow"))
        
        try:
            capture = EnhancedScreenCapture(self.logger)
            self.record_test("Enhanced Screen Capture - Initialization", True)
            
            # Test available capture methods
            available_methods = len(capture.capture_methods)
            print(f"   Available Methods: {available_methods}")
            self.record_test("Enhanced Screen Capture - Methods Available", available_methods > 0)
            
            # Test screen capture with different methods
            test_region = (100, 100, 200, 200)  # Small test region
            
            for method in capture.capture_methods.keys():
                try:
                    frame = capture._capture_with_method(method, *test_region)
                    success = frame is not None and frame.size > 0
                    self.record_test(f"Screen Capture - {method.value}", success)
                    
                    if success:
                        print(f"   ✅ {method.value}: {frame.shape}")
                    else:
                        print(f"   ❌ {method.value}: Failed")
                        
                except Exception as e:
                    print(f"   ❌ {method.value}: {str(e)[:50]}...")
                    self.record_test(f"Screen Capture - {method.value}", False)
            
            # Test automatic fallback
            frame = capture.capture_screen_region(*test_region)
            self.record_test("Enhanced Screen Capture - Fallback System", frame is not None)
            
            # Test performance metrics
            performance_report = capture.get_performance_report()
            self.record_test("Enhanced Screen Capture - Performance Metrics", len(performance_report) > 0)
            
        except Exception as e:
            self.logger.error(f"Enhanced screen capture test failed: {e}")
            self.record_test("Enhanced Screen Capture - Overall", False)
    
    def test_input_method_manager(self):
        """Test input method manager functionality"""
        print(colored("\n🖱️  Testing Input Method Manager", "yellow"))
        
        try:
            config = InputConfig(
                method=InputMethod.WIN32_SENDINPUT,
                humanization_enabled=True,
                movement_smoothing=5
            )
            input_manager = InputMethodManager(config, self.logger)
            self.record_test("Input Method Manager - Initialization", True)
            
            # Test available input methods
            available_methods = len(input_manager.input_methods)
            print(f"   Available Methods: {available_methods}")
            self.record_test("Input Method Manager - Methods Available", available_methods > 0)
            
            # Test input method switching
            for method in input_manager.input_methods.keys():
                success = input_manager.switch_input_method(method)
                self.record_test(f"Input Method - Switch to {method.value}", success)
                
                if success:
                    print(f"   ✅ {method.value}: Available")
                else:
                    print(f"   ❌ {method.value}: Failed")
            
            # Test movement curve generation (without actual input)
            try:
                curve = input_manager._generate_movement_curve(100, 50, 150)
                self.record_test("Input Method Manager - Movement Curves", len(curve.points) > 0)
                print(f"   Movement Curve: {len(curve.points)} points, {curve.total_time:.3f}s")
            except Exception as e:
                self.record_test("Input Method Manager - Movement Curves", False)
                print(f"   Movement Curve Generation Failed: {e}")
            
            # Test performance stats
            stats = input_manager.get_performance_stats()
            self.record_test("Input Method Manager - Performance Stats", isinstance(stats, dict))
            
        except Exception as e:
            self.logger.error(f"Input method manager test failed: {e}")
            self.record_test("Input Method Manager - Overall", False)
    
    def test_performance_optimizer(self):
        """Test performance optimizer functionality"""
        print(colored("\n⚡ Testing Performance Optimizer", "yellow"))
        
        try:
            optimizer = PerformanceOptimizer(self.logger)
            self.record_test("Performance Optimizer - Initialization", True)
            
            # Test performance monitoring
            optimizer.start_monitoring()
            time.sleep(2)  # Let it collect some data
            
            # Test frame metrics update
            optimizer.update_frame_metrics(0.016, 0.010, 0.005)  # 60 FPS simulation
            self.record_test("Performance Optimizer - Metrics Update", True)
            
            # Test performance report
            report = optimizer.get_performance_report()
            self.record_test("Performance Optimizer - Report Generation", len(report) > 0)
            print(f"   Current FPS: {optimizer.metrics.fps:.1f}")
            print(f"   Memory Usage: {optimizer.metrics.memory_usage:.1f}MB")
            
            # Test frame buffer
            frame_buffer = optimizer.create_frame_buffer(3)
            self.record_test("Performance Optimizer - Frame Buffer", frame_buffer is not None)
            
            optimizer.stop_monitoring()
            
        except Exception as e:
            self.logger.error(f"Performance optimizer test failed: {e}")
            self.record_test("Performance Optimizer - Overall", False)
    
    def test_system_integration_manager(self):
        """Test system integration manager functionality"""
        print(colored("\n🔧 Testing System Integration Manager", "yellow"))
        
        try:
            config = SystemConfiguration(
                auto_detect_display_mode=True,
                enable_performance_monitoring=True,
                auto_optimize=True
            )
            
            system_manager = SystemIntegrationManager(config, self.logger)
            self.record_test("System Integration Manager - Initialization", True)
            
            # Test component initialization
            components = {
                "Display Detector": system_manager.display_detector is not None,
                "Screen Capture": system_manager.screen_capture is not None,
                "Input Manager": system_manager.input_manager is not None,
                "Performance Optimizer": system_manager.performance_optimizer is not None
            }
            
            for component, status in components.items():
                self.record_test(f"System Integration - {component}", status)
                print(f"   {component}: {'✅ OK' if status else '❌ FAILED'}")
            
            # Test compatibility check
            system_manager._perform_compatibility_check()
            self.record_test("System Integration Manager - Compatibility Check", True)
            
            # Test status report
            status_report = system_manager.get_system_status_report()
            self.record_test("System Integration Manager - Status Report", len(status_report) > 0)
            
            # Test monitoring
            system_manager.start_monitoring()
            time.sleep(2)
            system_manager.stop_monitoring()
            self.record_test("System Integration Manager - Monitoring", True)
            
            # Cleanup
            system_manager.shutdown()
            
        except Exception as e:
            self.logger.error(f"System integration manager test failed: {e}")
            traceback.print_exc()
            self.record_test("System Integration Manager - Overall", False)
    
    def record_test(self, test_name: str, passed: bool):
        """Record test result"""
        self.test_results[test_name] = passed
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
    
    def show_test_summary(self):
        """Show comprehensive test summary"""
        print(colored("\n" + "=" * 80, "cyan"))
        print(colored("TEST SUMMARY", "cyan"))
        print(colored("=" * 80, "cyan"))
        
        # Overall statistics
        pass_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.total_tests - self.passed_tests}")
        print(f"Pass Rate: {pass_rate:.1f}%")
        
        # Detailed results
        print(colored("\nDETAILED RESULTS:", "yellow"))
        for test_name, passed in self.test_results.items():
            status = colored("✅ PASS", "green") if passed else colored("❌ FAIL", "red")
            print(f"  {test_name}: {status}")
        
        # Technical implementation status
        print(colored("\nTECHNICAL IMPLEMENTATION STATUS:", "yellow"))
        
        core_components = [
            "Display Mode Detector - Initialization",
            "Enhanced Screen Capture - Initialization", 
            "Input Method Manager - Initialization",
            "Performance Optimizer - Initialization",
            "System Integration Manager - Initialization"
        ]
        
        all_core_passed = all(self.test_results.get(comp, False) for comp in core_components)
        
        if all_core_passed:
            print(colored("🎉 All core technical components initialized successfully!", "green"))
            print(colored("✅ Technical implementation from documentation is working!", "green"))
        else:
            print(colored("⚠️  Some core components failed to initialize", "yellow"))
            print(colored("❌ Technical implementation needs attention", "red"))
        
        # Recommendations
        print(colored("\nRECOMMENDATIONS:", "yellow"))
        
        if pass_rate >= 90:
            print("• Technical implementation is excellent")
            print("• All major components are working correctly")
            print("• System is ready for production use")
        elif pass_rate >= 70:
            print("• Technical implementation is good")
            print("• Minor issues detected, but core functionality works")
            print("• Consider investigating failed tests")
        else:
            print("• Technical implementation needs improvement")
            print("• Multiple components have issues")
            print("• Review failed tests and fix underlying problems")


def main():
    """Main test entry point"""
    print(colored("Starting Technical Implementation Test Suite...", "cyan"))
    
    tester = TechnicalImplementationTester()
    tester.run_all_tests()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
