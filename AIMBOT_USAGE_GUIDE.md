# 🎯 ULTIMATE AI AIMBOT - Usage Guide

## ✅ FIXED ISSUES

### 1. **Window Spawning Bug RESOLVED** ✅
- **Problem**: Multiple overlay windows were spawning repeatedly
- **Solution**: Implemented proper window management with single window creation
- **Result**: Only ONE stable overlay window appears

### 2. **Comprehensive Settings GUI Added** ✅
- **Real-time settings adjustment** without restarting
- **Intuitive sliders and toggles** for all parameters
- **Save/Load configurations** for different setups
- **Live status monitoring** of aimbot state

---

## 🚀 LAUNCH OPTIONS

### Option 1: Easy Launcher (Recommended)
```bash
python launch_aimbot.py
```
- **🎯 Launch Aimbot Only**: Basic overlay without settings GUI
- **🎛️ Launch with Settings GUI**: Full featured with real-time controls
- **⚙️ Settings GUI Only**: Configure settings without running aimbot

### Option 2: Direct Launch
```bash
# Aimbot only
python visual_aimbot_overlay.py

# Aimbot with GUI
python visual_aimbot_overlay.py --with-gui

# Settings GUI only
python aimbot_settings_gui.py
```

---

## 🎮 CONTROLS

### **Aimbot Hotkeys**
- **F1**: 🔥 **TOGGLE AIMBOT ON/OFF** (starts INACTIVE)
- **F2**: 🎛️ **Open Settings GUI**
- **Q**: ❌ **Quit aimbot**
- **S**: ⚙️ **Open Settings GUI** (from overlay window)

### **Overlay Controls**
- **T**: Toggle target display
- **F**: Toggle FOV circle
- **C**: Toggle crosshair

---

## 🎛️ SETTINGS GUI FEATURES

### **📊 Performance Tab**
- **🎯 FOV Size**: Adjust detection area (200-800 pixels)
- **🖱️ Mouse Sensitivity**: Control movement speed (0.1-2.0)
- **🎯 Confidence Threshold**: Target detection accuracy (0.1-1.0)
- **🌊 Mouse Smoothing**: Movement smoothness (0.1-1.0)

### **👁️ Visual Tab**
- **🔵 Show FOV Circle**: Toggle field of view indicator
- **🎯 Show Target Boxes**: Toggle target highlighting
- **➕ Show Crosshair**: Toggle center crosshair
- **📊 Show Statistics**: Toggle FPS and detection stats
- **🔮 Show Prediction Lines**: Toggle aim prediction

### **🎮 Controls Tab**
- **🔥 Activation Hotkey**: Choose F1-F8 for toggle
- **🔍 Detection Method**: Select detection algorithm

### **💾 Configuration Management**
- **Save Settings**: Store current configuration
- **Load Settings**: Restore saved configuration
- **Reset Defaults**: Return to default values
- **Auto-apply**: Real-time setting updates

---

## ⚠️ SAFETY FEATURES

### **🛡️ Safe Startup**
- **Aimbot starts INACTIVE** by default
- **Full mouse control** until you press F1
- **Visual overlay shows** without mouse interference

### **🎮 Gaming-Friendly**
- **Toggle activation** for menus and navigation
- **No game interference** when inactive
- **Stable single window** overlay

---

## 📋 STATUS INDICATORS

### **In Overlay Window**
- **🔥 AIMBOT: ACTIVE** (Green) - Mouse movement enabled
- **💤 AIMBOT: INACTIVE** (Red) - Full mouse control
- **Press F1 to toggle** - Hotkey reminder

### **In Settings GUI**
- **🔗 Connection Status** - Shows if connected to aimbot
- **📊 Real-time Values** - Current setting values
- **🔄 Auto-apply Status** - Whether changes apply immediately

---

## 🔧 TYPICAL WORKFLOW

1. **Launch** using `python launch_aimbot.py`
2. **Choose** "Launch with Settings GUI" for full control
3. **Adjust settings** in real-time while seeing effects
4. **Save configuration** once you find optimal settings
5. **Press F1** when you want aimbot to take control
6. **Press F1 again** to regain mouse control for menus
7. **Use hotkeys** to toggle visual elements as needed

---

## 🎯 RECOMMENDED SETTINGS

### **For Beginners**
- FOV Size: 400
- Sensitivity: 0.5
- Confidence: 0.7
- Smoothing: 0.9

### **For Advanced Users**
- FOV Size: 600
- Sensitivity: 0.8
- Confidence: 0.6
- Smoothing: 0.7

### **For High Precision**
- FOV Size: 300
- Sensitivity: 0.3
- Confidence: 0.8
- Smoothing: 0.95

---

## 🆘 TROUBLESHOOTING

### **Window Issues**
- ✅ **FIXED**: Multiple windows no longer spawn
- If overlay doesn't appear, check if antivirus is blocking

### **Mouse Control**
- **Can't control mouse**: Press F1 to deactivate aimbot
- **Too sensitive**: Lower sensitivity in settings GUI
- **Too slow**: Increase sensitivity or lower smoothing

### **Settings Not Applying**
- Enable "Auto-apply changes" in settings GUI
- Or click "Apply Now" button manually

### **Hotkeys Not Working**
- Run as administrator if needed
- Check if other software is using same hotkeys

---

## 📁 FILES CREATED

- `visual_aimbot_overlay.py` - Main aimbot with fixed window management
- `aimbot_settings_gui.py` - Comprehensive settings interface
- `launch_aimbot.py` - Easy launcher with multiple options
- `aimbot_settings.json` - Saved configuration file (auto-created)

---

## 🎉 ENJOY YOUR ENHANCED AIMBOT!

The aimbot now provides:
- ✅ **Stable single window** overlay
- ✅ **Real-time settings control**
- ✅ **Safe toggle activation**
- ✅ **Gaming-friendly interface**
- ✅ **Professional configuration management**

**Remember**: Always use responsibly and in accordance with game terms of service!
