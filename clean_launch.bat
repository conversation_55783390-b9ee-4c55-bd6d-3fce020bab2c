@echo off
title 🎯 CLEAN AIMBOT LAUNCHER
color 0A

echo.
echo ========================================
echo    🎯 CLEAN STEALTH AIMBOT LAUNCHER
echo ========================================
echo.
echo Stopping any running aimbot processes...

REM Kill any Python processes that might be running aimbots
taskkill /f /im python.exe 2>nul
timeout /t 2 /nobreak >nul

echo.
echo ✅ Previous processes stopped
echo.
echo Starting Stealth Aimbot (Background Only)...
echo • No visual overlays
echo • Minimal screen capture
echo • Console feedback only
echo • Press Ctrl+C to stop
echo.

python stealth_aimbot.py

echo.
echo 🛑 Aimbot stopped. Press any key to exit...
pause > nul
