"""
Input Method Manager
Implements input simulation methods from AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md
Provides multiple input methods with automatic fallbacks and humanization
"""

import ctypes
import ctypes.wintypes
import time
import random
import math
from dataclasses import dataclass
from enum import Enum
from typing import Optional, Tuple, List, Callable
import numpy as np


class InputMethod(Enum):
    """Input simulation methods from technical documentation"""
    WIN32_SETCURSORPOS = "win32_setcursorpos"  # Direct cursor positioning
    WIN32_SENDINPUT = "win32_sendinput"  # Windows SendInput API
    WIN32_MOUSE_EVENT = "win32_mouse_event"  # Legacy mouse_event API
    DDXOFT = "ddxoft"  # DDXoft mouse library
    XBOX_CONTROLLER = "xbox_controller"  # Xbox controller simulation


@dataclass
class InputConfig:
    """Input method configuration"""
    method: InputMethod = InputMethod.WIN32_SENDINPUT
    humanization_enabled: bool = True
    movement_smoothing: int = 5
    timing_variance: float = 0.02
    movement_variance: float = 0.05
    reaction_time_min: float = 0.15
    reaction_time_max: float = 0.35


@dataclass
class MovementCurve:
    """Movement curve for humanized input"""
    points: List[Tuple[float, float]]
    total_time: float
    curve_type: str = "bezier"


# Windows API structures for SendInput
class POINT(ctypes.Structure):
    _fields_ = [("x", ctypes.c_long), ("y", ctypes.c_long)]


class MOUSEINPUT(ctypes.Structure):
    _fields_ = [("dx", ctypes.wintypes.LONG),
                ("dy", ctypes.wintypes.LONG),
                ("mouseData", ctypes.wintypes.DWORD),
                ("dwFlags", ctypes.wintypes.DWORD),
                ("time", ctypes.wintypes.DWORD),
                ("dwExtraInfo", ctypes.POINTER(ctypes.wintypes.ULONG))]


class INPUT(ctypes.Structure):
    class _INPUT(ctypes.Union):
        _fields_ = [("mi", MOUSEINPUT)]
    
    _anonymous_ = ("_input",)
    _fields_ = [("type", ctypes.wintypes.DWORD),
                ("_input", _INPUT)]


class InputMethodManager:
    """
    Manages different input simulation methods
    Implements concepts from AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md
    """
    
    def __init__(self, config: InputConfig, logger):
        self.config = config
        self.logger = logger
        self.current_method = config.method
        
        # Input method implementations
        self.input_methods = {}
        self.ddxoft_dll = None
        self.xbox_controller = None
        
        # Humanization state
        self.last_movement_time = 0.0
        self.movement_history = []
        self.fatigue_factor = 1.0
        
        self._initialize_input_methods()
        self.logger.info(f"Input method manager initialized with {self.current_method.value}")
    
    def _initialize_input_methods(self):
        """Initialize available input methods"""
        
        # Win32 SetCursorPos - Direct positioning
        self.input_methods[InputMethod.WIN32_SETCURSORPOS] = self._move_setcursorpos
        
        # Win32 SendInput - Recommended method
        self.input_methods[InputMethod.WIN32_SENDINPUT] = self._move_sendinput
        
        # Win32 mouse_event - Legacy method
        self.input_methods[InputMethod.WIN32_MOUSE_EVENT] = self._move_mouse_event
        
        # DDXoft - External library
        try:
            self.ddxoft_dll = ctypes.CDLL("./lib/mouse/DD94687.64.dll")
            self.ddxoft_dll.DD_btn.argtypes = [ctypes.c_int]
            self.ddxoft_dll.DD_movR.argtypes = [ctypes.c_int, ctypes.c_int]
            self.input_methods[InputMethod.DDXOFT] = self._move_ddxoft
            self.logger.info("DDXoft input method initialized")
        except Exception as e:
            self.logger.warning(f"DDXoft initialization failed: {e}")
        
        # Xbox Controller - For console-style input
        try:
            import sys
            import os
            sys.path.append(os.path.dirname(__file__))
            from xbox_controller import XboxControllerInput
            self.xbox_controller = XboxControllerInput(self.logger)
            self.input_methods[InputMethod.XBOX_CONTROLLER] = self._move_xbox_controller
            self.logger.info("Xbox controller input method initialized")
        except Exception as e:
            self.logger.warning(f"Xbox controller initialization failed: {e}")
    
    def move_to_target(self, target_x: int, target_y: int, current_x: int, current_y: int) -> bool:
        """
        Move to target using current input method with humanization
        Implements human behavior simulation from technical documentation
        """
        
        # Calculate movement vector
        dx = target_x - current_x
        dy = target_y - current_y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance < 2:  # Already at target
            return True
        
        # Apply humanization if enabled
        if self.config.humanization_enabled:
            # Add reaction time delay
            reaction_time = random.uniform(self.config.reaction_time_min, self.config.reaction_time_max)
            time.sleep(reaction_time)
            
            # Generate humanized movement curve
            movement_curve = self._generate_movement_curve(dx, dy, distance)
            return self._execute_movement_curve(movement_curve, current_x, current_y)
        else:
            # Direct movement
            return self._execute_direct_movement(dx, dy)
    
    def _generate_movement_curve(self, dx: float, dy: float, distance: float) -> MovementCurve:
        """Generate humanized movement curve"""
        
        # Determine curve type based on distance
        if distance > 100:
            curve_type = "bezier"
            smoothing_points = max(5, min(15, int(distance / 20)))
        else:
            curve_type = "linear"
            smoothing_points = max(3, int(distance / 10))
        
        # Generate control points for Bezier curve
        if curve_type == "bezier":
            points = self._generate_bezier_curve(dx, dy, smoothing_points)
        else:
            points = self._generate_linear_curve(dx, dy, smoothing_points)
        
        # Calculate total movement time based on distance and fatigue
        base_time = distance / 2000.0  # Base speed: 2000 pixels/second
        total_time = base_time * self.fatigue_factor
        
        # Add timing variance
        if self.config.humanization_enabled:
            variance = random.uniform(-self.config.timing_variance, self.config.timing_variance)
            total_time *= (1.0 + variance)
        
        return MovementCurve(points, total_time, curve_type)
    
    def _generate_bezier_curve(self, dx: float, dy: float, num_points: int) -> List[Tuple[float, float]]:
        """Generate Bezier curve points for natural movement"""
        
        # Control points for cubic Bezier curve
        p0 = (0.0, 0.0)  # Start point
        p3 = (dx, dy)    # End point
        
        # Generate control points with some randomness
        control_offset = 0.3
        p1 = (
            dx * 0.25 + random.uniform(-abs(dx) * control_offset, abs(dx) * control_offset),
            dy * 0.25 + random.uniform(-abs(dy) * control_offset, abs(dy) * control_offset)
        )
        p2 = (
            dx * 0.75 + random.uniform(-abs(dx) * control_offset, abs(dx) * control_offset),
            dy * 0.75 + random.uniform(-abs(dy) * control_offset, abs(dy) * control_offset)
        )
        
        # Generate curve points
        points = []
        for i in range(num_points + 1):
            t = i / num_points
            
            # Cubic Bezier formula
            x = (1-t)**3 * p0[0] + 3*(1-t)**2*t * p1[0] + 3*(1-t)*t**2 * p2[0] + t**3 * p3[0]
            y = (1-t)**3 * p0[1] + 3*(1-t)**2*t * p1[1] + 3*(1-t)*t**2 * p2[1] + t**3 * p3[1]
            
            # Add micro-movements for realism
            if self.config.humanization_enabled and i > 0 and i < num_points:
                x += random.uniform(-1, 1)
                y += random.uniform(-1, 1)
            
            points.append((x, y))
        
        return points
    
    def _generate_linear_curve(self, dx: float, dy: float, num_points: int) -> List[Tuple[float, float]]:
        """Generate linear movement points with slight variations"""
        
        points = []
        for i in range(num_points + 1):
            t = i / num_points
            
            x = dx * t
            y = dy * t
            
            # Add slight variations for humanization
            if self.config.humanization_enabled and i > 0 and i < num_points:
                variance = self.config.movement_variance
                x += random.uniform(-variance, variance) * abs(dx)
                y += random.uniform(-variance, variance) * abs(dy)
            
            points.append((x, y))
        
        return points
    
    def _execute_movement_curve(self, curve: MovementCurve, start_x: int, start_y: int) -> bool:
        """Execute movement curve using current input method"""
        
        if not curve.points:
            return False
        
        # Calculate time per step
        time_per_step = curve.total_time / len(curve.points) if len(curve.points) > 1 else 0.0
        
        # Execute movement
        last_point = (0.0, 0.0)
        
        for i, point in enumerate(curve.points[1:], 1):  # Skip first point (0,0)
            # Calculate relative movement
            rel_x = point[0] - last_point[0]
            rel_y = point[1] - last_point[1]
            
            # Execute movement step
            if abs(rel_x) > 0.5 or abs(rel_y) > 0.5:  # Only move if significant
                success = self._execute_input_method(int(rel_x), int(rel_y))
                if not success:
                    return False
            
            # Wait for next step
            if i < len(curve.points) - 1:  # Don't wait after last step
                step_time = time_per_step
                
                # Add micro-delays for realism
                if self.config.humanization_enabled:
                    micro_delay = random.uniform(-0.001, 0.001)
                    step_time = max(0.001, step_time + micro_delay)
                
                time.sleep(step_time)
            
            last_point = point
        
        # Update fatigue factor (slight performance degradation over time)
        if self.config.humanization_enabled:
            self.fatigue_factor = min(1.2, self.fatigue_factor + 0.001)
        
        return True
    
    def _execute_direct_movement(self, dx: int, dy: int) -> bool:
        """Execute direct movement without humanization"""
        return self._execute_input_method(dx, dy)
    
    def _execute_input_method(self, dx: int, dy: int) -> bool:
        """Execute movement using current input method"""
        
        if self.current_method not in self.input_methods:
            self.logger.error(f"Input method {self.current_method.value} not available")
            return False
        
        try:
            return self.input_methods[self.current_method](dx, dy)
        except Exception as e:
            self.logger.error(f"Input method {self.current_method.value} failed: {e}")
            return False
    
    def _move_setcursorpos(self, dx: int, dy: int) -> bool:
        """Move using SetCursorPos (absolute positioning)"""
        try:
            # Get current cursor position
            cursor_pos = POINT()
            ctypes.windll.user32.GetCursorPos(ctypes.byref(cursor_pos))
            
            # Calculate new position
            new_x = cursor_pos.x + dx
            new_y = cursor_pos.y + dy
            
            # Set new cursor position
            return bool(ctypes.windll.user32.SetCursorPos(new_x, new_y))
        except Exception as e:
            self.logger.error(f"SetCursorPos failed: {e}")
            return False
    
    def _move_sendinput(self, dx: int, dy: int) -> bool:
        """Move using SendInput API (recommended method)"""
        try:
            # Create INPUT structure
            extra = ctypes.c_ulong(0)
            ii_ = INPUT()
            ii_.type = 0  # INPUT_MOUSE
            ii_.mi = MOUSEINPUT(dx, dy, 0, 0x0001, 0, ctypes.pointer(extra))  # MOUSEEVENTF_MOVE
            
            # Send input
            result = ctypes.windll.user32.SendInput(1, ctypes.byref(ii_), ctypes.sizeof(ii_))
            return result == 1
        except Exception as e:
            self.logger.error(f"SendInput failed: {e}")
            return False
    
    def _move_mouse_event(self, dx: int, dy: int) -> bool:
        """Move using mouse_event API (legacy method)"""
        try:
            ctypes.windll.user32.mouse_event(0x0001, dx, dy, 0, 0)  # MOUSEEVENTF_MOVE
            return True
        except Exception as e:
            self.logger.error(f"mouse_event failed: {e}")
            return False
    
    def _move_ddxoft(self, dx: int, dy: int) -> bool:
        """Move using DDXoft library"""
        try:
            if self.ddxoft_dll:
                result = self.ddxoft_dll.DD_movR(dx, dy)
                return result == 1
            return False
        except Exception as e:
            self.logger.error(f"DDXoft move failed: {e}")
            return False
    
    def _move_xbox_controller(self, dx: int, dy: int) -> bool:
        """Move using Xbox controller simulation"""
        try:
            if self.xbox_controller:
                # Convert pixel movement to controller stick input
                max_distance = 200.0  # Maximum expected movement distance
                stick_x = max(-1.0, min(1.0, dx / max_distance))
                stick_y = max(-1.0, min(1.0, dy / max_distance))
                
                return self.xbox_controller.set_stick_position(stick_x, stick_y)
            return False
        except Exception as e:
            self.logger.error(f"Xbox controller move failed: {e}")
            return False
    
    def click(self) -> bool:
        """Perform mouse click using current method"""
        try:
            if self.current_method == InputMethod.DDXOFT and self.ddxoft_dll:
                return bool(self.ddxoft_dll.DD_btn(1))  # Left click
            elif self.current_method == InputMethod.XBOX_CONTROLLER and self.xbox_controller:
                return self.xbox_controller.trigger_shoot()
            else:
                # Use Win32 API for click
                ctypes.windll.user32.mouse_event(0x0002, 0, 0, 0, 0)  # MOUSEEVENTF_LEFTDOWN
                time.sleep(0.01)  # Brief delay
                ctypes.windll.user32.mouse_event(0x0004, 0, 0, 0, 0)  # MOUSEEVENTF_LEFTUP
                return True
        except Exception as e:
            self.logger.error(f"Click failed: {e}")
            return False
    
    def switch_input_method(self, method: InputMethod) -> bool:
        """Switch to different input method"""
        if method in self.input_methods:
            self.current_method = method
            self.logger.info(f"Switched to input method: {method.value}")
            return True
        else:
            self.logger.error(f"Input method {method.value} not available")
            return False
    
    def reset_fatigue(self):
        """Reset fatigue factor (simulate rest)"""
        self.fatigue_factor = 1.0
        self.logger.debug("Fatigue factor reset")
    
    def get_performance_stats(self) -> dict:
        """Get input method performance statistics"""
        return {
            'current_method': self.current_method.value,
            'fatigue_factor': self.fatigue_factor,
            'available_methods': [method.value for method in self.input_methods.keys()],
            'humanization_enabled': self.config.humanization_enabled
        }
