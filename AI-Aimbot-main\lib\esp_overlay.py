"""
ESP Overlay System
Provides visual debugging and information display
Based on NeuralBot's overlay capabilities
"""

import cv2
import math
import time
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Tuple, Dict, Any
import numpy as np


class OverlayMode(Enum):
    """Different overlay display modes"""
    MINIMAL = "minimal"
    STANDARD = "standard"
    DEBUG = "debug"
    FULL = "full"


@dataclass
class OverlayConfig:
    """Configuration for ESP overlay"""
    mode: OverlayMode = OverlayMode.STANDARD
    show_fps: bool = True
    show_targets: bool = True
    show_tracking_info: bool = True
    show_crosshair: bool = True
    show_fov: bool = True
    show_aim_line: bool = True
    show_prediction: bool = True
    show_performance: bool = False
    
    # Colors (BGR format for OpenCV)
    color_crosshair: Tuple[int, int, int] = (0, 255, 0)
    color_fov: Tuple[int, int, int] = (255, 255, 0)
    color_target: Tuple[int, int, int] = (0, 255, 0)
    color_locked_target: Tuple[int, int, int] = (0, 0, 255)
    color_aim_line: Tuple[int, int, int] = (255, 255, 255)
    color_prediction: Tuple[int, int, int] = (255, 0, 255)
    color_text: Tuple[int, int, int] = (255, 255, 255)
    
    # Sizes
    crosshair_size: int = 10
    target_thickness: int = 2
    text_scale: float = 0.6
    text_thickness: int = 1
    show_sticky_bubble: bool = True
    sticky_bubble_color: Tuple[int, int, int] = (0, 165, 255)  # Orange
    sticky_bubble_thickness: int = 1


class ESPOverlay:
    """ESP overlay system for visual debugging and information display"""
    
    def __init__(self, config: OverlayConfig, logger):
        self.config = config
        self.logger = logger
        
        # Performance tracking
        self.frame_times = []
        self.max_frame_history = 30
        
        # Display state
        self.last_targets = []
        self.last_best_target = None
        self.last_controller_input = (0.0, 0.0)
        self.last_aim_method = "unknown"
        
        self.logger.info(f"ESP overlay initialized with mode: {config.mode.value}")
    
    def draw_overlay(self, frame: np.ndarray, overlay_data: Dict[str, Any]) -> np.ndarray:
        """
        Draw complete overlay on frame
        
        Args:
            frame: Input frame to draw on
            overlay_data: Dictionary containing overlay information
            
        Returns:
            Frame with overlay drawn
        """
        if frame is None:
            return frame
        
        overlay_frame = frame.copy()
        
        # Extract data
        targets = overlay_data.get('targets', [])
        best_target = overlay_data.get('best_target', None)
        fps = overlay_data.get('fps', 0)
        screen_center = overlay_data.get('screen_center', (frame.shape[1]//2, frame.shape[0]//2))
        fov_size = overlay_data.get('fov_size', 300)
        controller_input = overlay_data.get('controller_input', (0.0, 0.0))
        aim_method = overlay_data.get('aim_method', 'unknown')
        performance_data = overlay_data.get('performance', {})
        tracking_stats = overlay_data.get('tracking_stats', {})
        
        # Update internal state
        self.last_targets = targets
        self.last_best_target = best_target
        self.last_controller_input = controller_input
        self.last_aim_method = aim_method
        
        # Draw components based on mode
        if self.config.mode != OverlayMode.MINIMAL:
            if self.config.show_fov:
                self._draw_fov(overlay_frame, screen_center, fov_size)
            if self.config.show_sticky_bubble:
                self._draw_sticky_bubble(overlay_frame, screen_center, fov_size)
            
            if self.config.show_crosshair:
                self._draw_crosshair(overlay_frame, screen_center)
            
            if self.config.show_targets:
                self._draw_targets(overlay_frame, targets, best_target, screen_center)
            
            if self.config.show_aim_line and best_target:
                self._draw_aim_line(overlay_frame, screen_center, best_target)
            
            if self.config.show_prediction and best_target:
                self._draw_prediction(overlay_frame, best_target)
        
        # Draw information overlays
        if self.config.show_fps:
            self._draw_fps(overlay_frame, fps)
        
        if self.config.show_tracking_info and self.config.mode in [OverlayMode.DEBUG, OverlayMode.FULL]:
            self._draw_tracking_info(overlay_frame, tracking_stats)
        
        if self.config.show_performance and self.config.mode == OverlayMode.FULL:
            self._draw_performance_info(overlay_frame, performance_data)
        
        # Draw controller input visualization
        if self.config.mode in [OverlayMode.DEBUG, OverlayMode.FULL]:
            self._draw_controller_input(overlay_frame, controller_input, aim_method)
        
        return overlay_frame
    
    def _draw_crosshair(self, frame: np.ndarray, center: Tuple[int, int]) -> None:
        """Draw crosshair at screen center"""
        cx, cy = center
        size = self.config.crosshair_size
        color = self.config.color_crosshair
        
        # Horizontal line
        cv2.line(frame, (cx - size, cy), (cx + size, cy), color, 1)
        # Vertical line
        cv2.line(frame, (cx, cy - size), (cx, cy + size), color, 1)
        # Center dot
        cv2.circle(frame, (cx, cy), 2, color, -1)
    
    def _draw_fov(self, frame: np.ndarray, center: Tuple[int, int], fov_size: int) -> None:
        """Draw field of view circle"""
        cx, cy = center
        radius = fov_size // 2
        color = self.config.color_fov
        
        cv2.circle(frame, (cx, cy), radius, color, 1)

    def _draw_sticky_bubble(self, frame: np.ndarray, center: Tuple[int, int], fov_size: int) -> None:
        """Draw sticky bubble ring for accessibility calibration.
        Bubble radius is derived from fov_size/aimbot config via overlay_data if provided.
        """
        # Default visualization: fov-based ring; the caller should supply exact radius
        bubble_px = max(10, fov_size // 8)
        cv2.circle(frame, center, bubble_px, self.config.sticky_bubble_color, self.config.sticky_bubble_thickness)
    
    def _draw_targets(self, frame: np.ndarray, targets: List[Any], best_target: Any, 
                     center: Tuple[int, int]) -> None:
        """Draw target bounding boxes and information"""
        for target in targets:
            # Determine if this is the best target
            is_best = (best_target is not None and 
                      hasattr(target, 'id') and hasattr(best_target, 'id') and
                      target.id == best_target.id)
            
            color = self.config.color_locked_target if is_best else self.config.color_target
            
            # Draw bounding box
            if hasattr(target, 'x1'):  # TrackedTarget
                x1, y1, x2, y2 = target.x1, target.y1, target.x2, target.y2
                center_x, center_y = target.center_x, target.center_y
                confidence = getattr(target, 'confidence', 0.0)
                target_id = getattr(target, 'id', 0)
            else:  # Simple detection dict
                x1, y1, x2, y2 = target['x1'], target['y1'], target['x2'], target['y2']
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                confidence = target.get('confidence', 0.0)
                target_id = target.get('id', 0)
            
            # Draw bounding rectangle
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, self.config.target_thickness)
            
            # Draw center point
            cv2.circle(frame, (center_x, center_y), 3, color, -1)
            
            # Draw target information
            if self.config.mode in [OverlayMode.DEBUG, OverlayMode.FULL]:
                # Distance to center
                distance = math.sqrt((center_x - center[0])**2 + (center_y - center[1])**2)
                
                # Info text
                info_lines = [
                    f"ID: {target_id}",
                    f"Conf: {confidence:.2f}",
                    f"Dist: {distance:.0f}"
                ]
                
                # Add tracking info if available
                if hasattr(target, 'tracking_time'):
                    info_lines.append(f"Track: {target.tracking_time:.1f}s")
                
                # Draw info text
                for i, line in enumerate(info_lines):
                    text_y = y1 - 10 - (i * 15)
                    if text_y > 10:  # Make sure text is visible
                        cv2.putText(frame, line, (x1, text_y), cv2.FONT_HERSHEY_SIMPLEX,
                                  self.config.text_scale, self.config.color_text, 
                                  self.config.text_thickness)
    
    def _draw_aim_line(self, frame: np.ndarray, center: Tuple[int, int], target: Any) -> None:
        """Draw line from crosshair to target"""
        if hasattr(target, 'center_x'):
            target_pos = (target.center_x, target.center_y)
        else:
            target_pos = ((target['x1'] + target['x2']) // 2, (target['y1'] + target['y2']) // 2)
        
        cv2.line(frame, center, target_pos, self.config.color_aim_line, 1)
        
        # Draw distance text
        distance = math.sqrt((target_pos[0] - center[0])**2 + (target_pos[1] - center[1])**2)
        mid_x = (center[0] + target_pos[0]) // 2
        mid_y = (center[1] + target_pos[1]) // 2
        
        cv2.putText(frame, f"{distance:.0f}px", (mid_x, mid_y), cv2.FONT_HERSHEY_SIMPLEX,
                   self.config.text_scale, self.config.color_text, self.config.text_thickness)
    
    def _draw_prediction(self, frame: np.ndarray, target: Any) -> None:
        """Draw predicted target position"""
        if not hasattr(target, 'predicted_x'):
            return
        
        predicted_pos = (int(target.predicted_x), int(target.predicted_y))
        current_pos = (target.center_x, target.center_y)
        
        # Draw prediction circle
        cv2.circle(frame, predicted_pos, 5, self.config.color_prediction, 2)
        
        # Draw velocity vector
        cv2.arrowedLine(frame, current_pos, predicted_pos, self.config.color_prediction, 1)
        
        # Draw velocity text
        if hasattr(target, 'velocity_x'):
            velocity = math.sqrt(target.velocity_x**2 + target.velocity_y**2)
            cv2.putText(frame, f"V: {velocity:.0f}px/s", 
                       (predicted_pos[0] + 10, predicted_pos[1]), 
                       cv2.FONT_HERSHEY_SIMPLEX, self.config.text_scale,
                       self.config.color_prediction, self.config.text_thickness)
    
    def _draw_fps(self, frame: np.ndarray, fps: int) -> None:
        """Draw FPS counter"""
        fps_text = f"FPS: {fps}"
        cv2.putText(frame, fps_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX,
                   self.config.text_scale, self.config.color_text, self.config.text_thickness)
    
    def _draw_tracking_info(self, frame: np.ndarray, tracking_stats: Dict[str, Any]) -> None:
        """Draw tracking statistics"""
        if not tracking_stats:
            return
        
        y_offset = 60
        info_lines = [
            f"Active Targets: {tracking_stats.get('active_targets', 0)}",
            f"Total Tracks: {tracking_stats.get('total_tracks', 0)}",
            f"Success Rate: {tracking_stats.get('success_rate', 0):.1f}%"
        ]
        
        for line in info_lines:
            cv2.putText(frame, line, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX,
                       self.config.text_scale, self.config.color_text, self.config.text_thickness)
            y_offset += 20
    
    def _draw_performance_info(self, frame: np.ndarray, performance_data: Dict[str, Any]) -> None:
        """Draw performance metrics"""
        if not performance_data:
            return
        
        y_offset = frame.shape[0] - 100  # Bottom of screen
        
        info_lines = [
            f"Frame Time: {performance_data.get('frame_time', 0)*1000:.1f}ms",
            f"Detection Time: {performance_data.get('detection_time', 0)*1000:.1f}ms",
            f"Memory: {performance_data.get('memory_usage', 0):.0f}MB",
            f"CPU: {performance_data.get('cpu_usage', 0):.1f}%"
        ]
        
        for line in info_lines:
            cv2.putText(frame, line, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX,
                       self.config.text_scale, self.config.color_text, self.config.text_thickness)
            y_offset += 20
    
    def _draw_controller_input(self, frame: np.ndarray, controller_input: Tuple[float, float], 
                              aim_method: str) -> None:
        """Draw controller input visualization"""
        x_input, y_input = controller_input
        
        # Draw controller input indicator in top-right corner
        indicator_x = frame.shape[1] - 120
        indicator_y = 30
        
        # Draw method name
        cv2.putText(frame, f"Method: {aim_method}", (indicator_x, indicator_y), 
                   cv2.FONT_HERSHEY_SIMPLEX, self.config.text_scale,
                   self.config.color_text, self.config.text_thickness)
        
        # Draw input values
        cv2.putText(frame, f"X: {x_input:+.2f}", (indicator_x, indicator_y + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, self.config.text_scale,
                   self.config.color_text, self.config.text_thickness)
        
        cv2.putText(frame, f"Y: {y_input:+.2f}", (indicator_x, indicator_y + 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, self.config.text_scale,
                   self.config.color_text, self.config.text_thickness)
        
        # Draw visual stick representation
        stick_center = (indicator_x + 50, indicator_y + 70)
        stick_radius = 25
        
        # Draw stick base
        cv2.circle(frame, stick_center, stick_radius, self.config.color_text, 1)
        
        # Draw stick position
        stick_x = int(stick_center[0] + x_input * stick_radius * 0.8)
        stick_y = int(stick_center[1] + y_input * stick_radius * 0.8)
        cv2.circle(frame, (stick_x, stick_y), 3, self.config.color_crosshair, -1)
        
        # Draw magnitude
        magnitude = math.sqrt(x_input**2 + y_input**2)
        cv2.putText(frame, f"Mag: {magnitude:.2f}", (indicator_x, indicator_y + 110), 
                   cv2.FONT_HERSHEY_SIMPLEX, self.config.text_scale,
                   self.config.color_text, self.config.text_thickness)
    
    def set_mode(self, mode: OverlayMode) -> None:
        """Change overlay display mode"""
        self.config.mode = mode
        self.logger.info(f"ESP overlay mode changed to: {mode.value}")
    
    def toggle_component(self, component: str) -> None:
        """Toggle specific overlay component"""
        if hasattr(self.config, f'show_{component}'):
            current_value = getattr(self.config, f'show_{component}')
            setattr(self.config, f'show_{component}', not current_value)
            self.logger.info(f"Toggled {component}: {not current_value}")
    
    def get_overlay_info(self) -> Dict[str, Any]:
        """Get current overlay configuration"""
        return {
            'mode': self.config.mode.value,
            'components': {
                'fps': self.config.show_fps,
                'targets': self.config.show_targets,
                'tracking_info': self.config.show_tracking_info,
                'crosshair': self.config.show_crosshair,
                'fov': self.config.show_fov,
                'aim_line': self.config.show_aim_line,
                'prediction': self.config.show_prediction,
                'performance': self.config.show_performance
            }
        }
