#!/usr/bin/env python3
"""
🎯 STEALTH AIMBOT - Background Only
==================================
Clean, minimal aimbot that runs in background without visual interference:
• No overlay windows or visual artifacts
• Small FOV capture only (200x200px)
• Console output for feedback
• Clean shutdown with Ctrl+C
• Minimal resource usage
"""

import cv2
import numpy as np
import mss
import time
import win32api
import win32gui
import signal
import sys
import threading
from collections import deque

class StealthAimbot:
    def __init__(self):
        self.running = False
        self.paused = False
        
        # Performance tracking
        self.fps = 0
        self.frame_times = deque(maxlen=60)
        self.total_detections = 0
        self.successful_locks = 0
        
        # Settings - minimal for performance
        self.fov_size = 200  # Small FOV to reduce interference
        self.sensitivity = 0.6
        self.confidence_threshold = 0.7
        self.smoothing = 0.8
        
        # Screen info
        self.screen_w = win32api.GetSystemMetrics(0)
        self.screen_h = win32api.GetSystemMetrics(1)
        
        # Screen capture (will be initialized in thread)
        self.sct = None
        
        # Setup clean shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        
        print("🎯 Stealth Aimbot initialized")
        print(f"📺 Screen: {self.screen_w}x{self.screen_h}")
        print(f"🎯 FOV: {self.fov_size}x{self.fov_size} (minimal capture)")
        print("🔇 Running in stealth mode - no visual interference")
        
    def signal_handler(self, signum, frame):
        """Handle Ctrl+C gracefully"""
        print("\n🛑 Shutdown signal received...")
        self.stop()
        
    def detect_targets(self, frame):
        """Lightweight target detection"""
        targets = []
        
        try:
            # Convert to HSV for color detection
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            
            # Optimized color ranges for common enemy colors
            color_ranges = [
                # Red enemies (most common)
                ([0, 120, 120], [10, 255, 255]),
                ([170, 120, 120], [180, 255, 255]),
                # Blue enemies
                ([100, 120, 120], [130, 255, 255])
            ]
            
            for i, (lower, upper) in enumerate(color_ranges):
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                
                # Quick morphological cleanup
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
                mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
                
                # Find contours
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    
                    # Filter by size (human-like targets)
                    if 150 < area < 2000:
                        x, y, w, h = cv2.boundingRect(contour)
                        
                        # Check aspect ratio (human proportions)
                        aspect_ratio = w / h if h > 0 else 0
                        if 0.4 < aspect_ratio < 2.5:
                            
                            # Calculate confidence based on area and shape
                            confidence = min((area / 1000.0) * (1.0 if 0.7 < aspect_ratio < 1.5 else 0.8), 1.0)
                            
                            if confidence > self.confidence_threshold:
                                targets.append({
                                    'x': x + w // 2,
                                    'y': y + h // 2,
                                    'w': w,
                                    'h': h,
                                    'confidence': confidence,
                                    'color_type': i
                                })
            
            # Remove overlapping targets
            filtered_targets = []
            for target in targets:
                is_duplicate = False
                for existing in filtered_targets:
                    distance = np.sqrt((target['x'] - existing['x'])**2 + (target['y'] - existing['y'])**2)
                    if distance < 25:  # Too close
                        if target['confidence'] > existing['confidence']:
                            filtered_targets.remove(existing)
                        else:
                            is_duplicate = True
                        break
                
                if not is_duplicate:
                    filtered_targets.append(target)
            
            return filtered_targets
            
        except Exception as e:
            print(f"⚠️ Detection error: {e}")
            return []
    
    def move_to_target(self, target, region):
        """Smooth mouse movement to target"""
        try:
            # Calculate screen coordinates
            target_screen_x = region['left'] + target['x']
            target_screen_y = region['top'] + target['y']
            
            # Get current mouse position
            current_x, current_y = win32gui.GetCursorPos()
            
            # Calculate movement with smoothing
            dx = (target_screen_x - current_x) * self.sensitivity * self.smoothing
            dy = (target_screen_y - current_y) * self.sensitivity * self.smoothing
            
            # Apply movement
            new_x = int(current_x + dx)
            new_y = int(current_y + dy)
            
            # Move mouse
            win32api.SetCursorPos((new_x, new_y))
            
            return True
            
        except Exception as e:
            print(f"⚠️ Movement error: {e}")
            return False
    
    def print_status(self, targets_count, best_confidence=0):
        """Print status to console"""
        status_parts = [
            f"FPS: {self.fps:.0f}",
            f"Targets: {targets_count}",
            f"Total: {self.total_detections}",
            f"Locks: {self.successful_locks}"
        ]
        
        if best_confidence > 0:
            status_parts.append(f"Best: {best_confidence:.2f}")
            
        status = " | ".join(status_parts)
        
        # Clear line and print status
        print(f"\r🎯 {status}", end="", flush=True)
    
    def run(self):
        """Main aimbot loop"""
        self.running = True
        
        print("🚀 Starting Stealth Aimbot...")
        print("🎮 Running in background - check console for status")
        print("🛑 Press Ctrl+C to stop cleanly")
        print("-" * 50)
        
        # Initialize screen capture in this thread
        self.sct = mss.mss()
        
        frame_count = 0
        last_status_time = time.time()
        
        try:
            while self.running:
                if self.paused:
                    time.sleep(0.1)
                    continue
                    
                frame_start = time.perf_counter()
                
                # Calculate capture region (small FOV around center)
                center_x = self.screen_w // 2
                center_y = self.screen_h // 2
                
                region = {
                    'left': center_x - self.fov_size // 2,
                    'top': center_y - self.fov_size // 2,
                    'width': self.fov_size,
                    'height': self.fov_size
                }
                
                # Capture small screen region
                screenshot = self.sct.grab(region)
                frame = np.array(screenshot)
                frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
                
                # Detect targets
                targets = self.detect_targets(frame)
                
                best_confidence = 0
                if targets:
                    self.total_detections += len(targets)
                    
                    # Find best target (highest confidence, closest to center)
                    center_fov_x, center_fov_y = self.fov_size // 2, self.fov_size // 2
                    
                    best_target = None
                    best_score = 0
                    
                    for target in targets:
                        # Distance from center
                        distance = np.sqrt((target['x'] - center_fov_x)**2 + (target['y'] - center_fov_y)**2)
                        distance_factor = max(0, 1 - (distance / (self.fov_size // 2)))
                        
                        # Combined score (confidence + proximity to center)
                        score = target['confidence'] * 0.7 + distance_factor * 0.3
                        
                        if score > best_score:
                            best_score = score
                            best_target = target
                            best_confidence = target['confidence']
                    
                    # Move to best target
                    if best_target and best_confidence > 0.8:
                        if self.move_to_target(best_target, region):
                            self.successful_locks += 1
                
                # Update performance tracking
                frame_time = time.perf_counter() - frame_start
                self.frame_times.append(frame_time)
                
                frame_count += 1
                if frame_count % 10 == 0:  # Update FPS every 10 frames
                    avg_frame_time = sum(self.frame_times) / len(self.frame_times)
                    self.fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
                
                # Print status every 0.5 seconds
                if time.time() - last_status_time > 0.5:
                    self.print_status(len(targets), best_confidence)
                    last_status_time = time.time()
                
                # Small delay to prevent excessive CPU usage
                time.sleep(0.001)
                
        except Exception as e:
            print(f"\n❌ Error in main loop: {e}")
        finally:
            self.cleanup()
    
    def stop(self):
        """Stop the aimbot"""
        self.running = False
        
    def pause(self):
        """Pause/unpause the aimbot"""
        self.paused = not self.paused
        status = "PAUSED" if self.paused else "RESUMED"
        print(f"\n⏸️ Aimbot {status}")
        
    def cleanup(self):
        """Clean shutdown"""
        self.running = False
        
        if self.sct:
            try:
                self.sct.close()
            except:
                pass
        
        print(f"\n🛑 Stealth Aimbot stopped")
        print(f"📊 Final Stats:")
        print(f"   • Total detections: {self.total_detections}")
        print(f"   • Successful locks: {self.successful_locks}")
        print(f"   • Final FPS: {self.fps:.0f}")
        print("👋 Clean shutdown complete")

def main():
    print("🎯 STEALTH AIMBOT - Background Only")
    print("=" * 40)
    print("Features:")
    print("• No visual overlays or windows")
    print("• Minimal 200x200px screen capture")
    print("• Console status updates only")
    print("• Clean Ctrl+C shutdown")
    print("• Optimized for minimal interference")
    print()
    
    try:
        aimbot = StealthAimbot()
        aimbot.run()
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Failed to start: {e}")
    finally:
        print("Exiting...")
        sys.exit(0)

if __name__ == "__main__":
    main()
