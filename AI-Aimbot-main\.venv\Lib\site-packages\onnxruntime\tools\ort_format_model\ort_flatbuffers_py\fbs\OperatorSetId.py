# automatically generated by the FlatBuffers compiler, do not modify

# namespace: fbs

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class OperatorSetId(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = OperatorSetId()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsOperatorSetId(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def OperatorSetIdBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4F\x52\x54\x4D", size_prefixed=size_prefixed)

    # OperatorSetId
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # OperatorSetId
    def Domain(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # OperatorSetId
    def Version(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Int64Flags, o + self._tab.Pos)
        return 0

def OperatorSetIdStart(builder):
    builder.StartObject(2)

def Start(builder):
    OperatorSetIdStart(builder)

def OperatorSetIdAddDomain(builder, domain):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(domain), 0)

def AddDomain(builder, domain):
    OperatorSetIdAddDomain(builder, domain)

def OperatorSetIdAddVersion(builder, version):
    builder.PrependInt64Slot(1, version, 0)

def AddVersion(builder, version):
    OperatorSetIdAddVersion(builder, version)

def OperatorSetIdEnd(builder):
    return builder.EndObject()

def End(builder):
    return OperatorSetIdEnd(builder)
