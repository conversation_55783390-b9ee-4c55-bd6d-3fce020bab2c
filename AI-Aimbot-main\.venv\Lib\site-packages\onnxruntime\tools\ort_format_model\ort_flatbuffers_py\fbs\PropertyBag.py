# automatically generated by the FlatBuffers compiler, do not modify

# namespace: fbs

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class PropertyBag(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = PropertyBag()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsPropertyBag(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def PropertyBagBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4F\x44\x54\x43", size_prefixed=size_prefixed)

    # PropertyBag
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # PropertyBag
    def Ints(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            from ort_flatbuffers_py.fbs.IntProperty import IntProperty
            obj = IntProperty()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # PropertyBag
    def IntsLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # PropertyBag
    def IntsIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        return o == 0

    # PropertyBag
    def Floats(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            from ort_flatbuffers_py.fbs.FloatProperty import FloatProperty
            obj = FloatProperty()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # PropertyBag
    def FloatsLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # PropertyBag
    def FloatsIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0

    # PropertyBag
    def Strings(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            from ort_flatbuffers_py.fbs.StringProperty import StringProperty
            obj = StringProperty()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # PropertyBag
    def StringsLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # PropertyBag
    def StringsIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        return o == 0

def PropertyBagStart(builder):
    builder.StartObject(3)

def Start(builder):
    PropertyBagStart(builder)

def PropertyBagAddInts(builder, ints):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(ints), 0)

def AddInts(builder, ints):
    PropertyBagAddInts(builder, ints)

def PropertyBagStartIntsVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def StartIntsVector(builder, numElems: int) -> int:
    return PropertyBagStartIntsVector(builder, numElems)

def PropertyBagAddFloats(builder, floats):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(floats), 0)

def AddFloats(builder, floats):
    PropertyBagAddFloats(builder, floats)

def PropertyBagStartFloatsVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def StartFloatsVector(builder, numElems: int) -> int:
    return PropertyBagStartFloatsVector(builder, numElems)

def PropertyBagAddStrings(builder, strings):
    builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(strings), 0)

def AddStrings(builder, strings):
    PropertyBagAddStrings(builder, strings)

def PropertyBagStartStringsVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def StartStringsVector(builder, numElems: int) -> int:
    return PropertyBagStartStringsVector(builder, numElems)

def PropertyBagEnd(builder):
    return builder.EndObject()

def End(builder):
    return PropertyBagEnd(builder)
