#!/usr/bin/env python3
"""
🧪 WINDOW MANAGEMENT TEST
=========================
Test script to verify the overlay window management works correctly
"""

import cv2
import numpy as np
import time
import threading

class WindowManagementTest:
    def __init__(self):
        self.window_name = 'TEST_OVERLAY'
        self.window_created = False
        self.window_exists = False
        self.running = True
        
    def check_window_exists(self):
        """Check if the OpenCV window actually exists"""
        try:
            prop = cv2.getWindowProperty(self.window_name, cv2.WND_PROP_VISIBLE)
            return prop >= 0
        except:
            return False
    
    def ensure_single_window(self):
        """Ensure only one window exists"""
        window_exists = self.check_window_exists()
        
        # If we think window is created but it doesn't exist, reset flag
        if self.window_created and not window_exists:
            print("⚠️ Window was destroyed externally, recreating...")
            self.window_created = False
            self.window_exists = False
        
        # Create window only if it doesn't exist
        if not self.window_created and not window_exists:
            try:
                cv2.destroyAllWindows()
                cv2.waitKey(1)
                
                cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO)
                cv2.resizeWindow(self.window_name, 400, 400)
                cv2.moveWindow(self.window_name, 200, 200)
                
                self.window_created = True
                self.window_exists = True
                print("✅ Test window created")
                
            except Exception as e:
                print(f"❌ Error creating window: {e}")
                self.window_created = False
                self.window_exists = False
    
    def run_test(self):
        """Run the window management test"""
        print("🧪 Starting Window Management Test")
        print("=" * 40)
        print("Instructions:")
        print("1. A test window will appear")
        print("2. Try moving, resizing, minimizing the window")
        print("3. Check that NO duplicate windows are created")
        print("4. Press 'q' in the window to quit")
        print("5. Watch the console for any duplicate creation messages")
        print()
        
        frame_count = 0
        
        try:
            while self.running:
                # Create test frame
                frame = np.zeros((400, 400, 3), dtype=np.uint8)
                
                # Add some visual content
                cv2.circle(frame, (200, 200), 100, (0, 255, 255), 2)  # Yellow circle
                cv2.line(frame, (180, 200), (220, 200), (0, 255, 0), 2)  # Green crosshair
                cv2.line(frame, (200, 180), (200, 220), (0, 255, 0), 2)
                
                # Add frame counter
                cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                # Add instructions
                cv2.putText(frame, "Try moving/resizing me!", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                cv2.putText(frame, "Press 'q' to quit", (10, 80), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                
                # Ensure single window
                self.ensure_single_window()
                
                # Update window
                if self.window_exists:
                    try:
                        cv2.imshow(self.window_name, frame)
                    except cv2.error as e:
                        print(f"⚠️ Window display error: {e}")
                        self.window_created = False
                        self.window_exists = False
                
                # Handle key presses
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q') or key == ord('Q'):
                    break
                
                frame_count += 1
                
                # Print status every 100 frames
                if frame_count % 100 == 0:
                    exists = self.check_window_exists()
                    print(f"📊 Frame {frame_count}: Window exists = {exists}, Created flag = {self.window_created}")
                
                time.sleep(0.01)  # Small delay
                
        except KeyboardInterrupt:
            print("\n🛑 Test interrupted by user")
        except Exception as e:
            print(f"❌ Test error: {e}")
        finally:
            self.running = False
            self.window_created = False
            self.window_exists = False
            
            try:
                cv2.destroyAllWindows()
                cv2.waitKey(1)
            except:
                pass
            
            print("✅ Window Management Test completed")
            print("If you saw NO duplicate windows, the fix is working!")

def main():
    test = WindowManagementTest()
    test.run_test()

if __name__ == "__main__":
    main()
