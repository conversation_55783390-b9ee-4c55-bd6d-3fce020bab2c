#!/usr/bin/env python3
"""
🎯 SIMPLE AIMBOT GUI - No Threading Issues
==========================================
Clean, simple GUI interface that avoids threading problems
"""

import tkinter as tk
from tkinter import ttk, messagebox
import time
import cv2
import numpy as np
import subprocess
import os
import threading
from PIL import Image, ImageTk

class SimpleAimbotGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 ULTIMATE AI AIMBOT - Simple Edition")
        self.root.geometry("800x600")
        self.root.configure(bg='#1a1a1a')
        
        # State
        self.current_process = None
        self.running = False
        
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the main GUI"""
        # Title
        title_frame = tk.Frame(self.root, bg='#1a1a1a')
        title_frame.pack(fill='x', padx=20, pady=20)
        
        title_label = tk.Label(title_frame, text="🎯 ULTIMATE AI AIMBOT", 
                              font=('Arial', 28, 'bold'), 
                              fg='#00ff00', bg='#1a1a1a')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="Professional Gaming Enhancement System", 
                                 font=('Arial', 14), 
                                 fg='#888888', bg='#1a1a1a')
        subtitle_label.pack()
        
        # Main content
        content_frame = tk.Frame(self.root, bg='#2a2a2a', relief='raised', bd=3)
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Engine selection
        engine_frame = tk.LabelFrame(content_frame, text="🚀 SELECT AIMBOT ENGINE", 
                                   fg='#00ff00', bg='#2a2a2a', 
                                   font=('Arial', 16, 'bold'))
        engine_frame.pack(fill='x', padx=20, pady=20)
        
        # Engine buttons
        button_frame = tk.Frame(engine_frame, bg='#2a2a2a')
        button_frame.pack(fill='x', padx=20, pady=20)
        
        # Lightning Fast Learner
        lightning_btn = tk.Button(button_frame, 
                                 text="⚡ LIGHTNING FAST LEARNER\n60 FPS • Adaptive Learning • Real-time Tracking", 
                                 command=lambda: self.launch_engine("lightning_fast_learner.py"),
                                 bg='#00aa00', fg='white', 
                                 font=('Arial', 12, 'bold'),
                                 relief='raised', bd=3, height=3)
        lightning_btn.pack(fill='x', pady=5)
        
        # CPU Optimized
        cpu_btn = tk.Button(button_frame, 
                           text="💻 CPU OPTIMIZED\nMulti-threaded • No GPU Required • High Performance", 
                           command=lambda: self.launch_engine("cpu_optimized_aimbot.py"),
                           bg='#0066cc', fg='white', 
                           font=('Arial', 12, 'bold'),
                           relief='raised', bd=3, height=3)
        cpu_btn.pack(fill='x', pady=5)
        
        # Ultimate Speed Hybrid
        hybrid_btn = tk.Button(button_frame, 
                              text="🚀 ULTIMATE SPEED HYBRID\nGPU Accelerated • <2ms Latency • Maximum Performance", 
                              command=lambda: self.launch_engine("ultimate_speed_hybrid.py"),
                              bg='#cc0066', fg='white', 
                              font=('Arial', 12, 'bold'),
                              relief='raised', bd=3, height=3)
        hybrid_btn.pack(fill='x', pady=5)
        
        # Original Lunar
        lunar_btn = tk.Button(button_frame, 
                             text="🌙 ORIGINAL LUNAR\nClassic • Reliable • Proven Performance", 
                             command=lambda: self.launch_engine("lunar.py"),
                             bg='#666600', fg='white', 
                             font=('Arial', 12, 'bold'),
                             relief='raised', bd=3, height=3)
        lunar_btn.pack(fill='x', pady=5)
        
        # Control buttons
        control_frame = tk.Frame(content_frame, bg='#2a2a2a')
        control_frame.pack(fill='x', padx=20, pady=20)
        
        self.stop_btn = tk.Button(control_frame, 
                                 text="🛑 STOP AIMBOT", 
                                 command=self.stop_aimbot,
                                 bg='#aa0000', fg='white', 
                                 font=('Arial', 14, 'bold'),
                                 relief='raised', bd=3, height=2)
        self.stop_btn.pack(side='left', fill='x', expand=True, padx=5)
        
        install_btn = tk.Button(control_frame, 
                               text="📦 INSTALL DEPENDENCIES", 
                               command=self.install_dependencies,
                               bg='#aa6600', fg='white', 
                               font=('Arial', 14, 'bold'),
                               relief='raised', bd=3, height=2)
        install_btn.pack(side='right', fill='x', expand=True, padx=5)
        
        # Status
        self.status_frame = tk.Frame(content_frame, bg='#2a2a2a')
        self.status_frame.pack(fill='x', padx=20, pady=10)
        
        self.status_label = tk.Label(self.status_frame, text="⚫ OFFLINE - Select an engine to start", 
                                   font=('Arial', 16, 'bold'), 
                                   fg='#ff6600', bg='#2a2a2a')
        self.status_label.pack()
        
        # Log area
        log_frame = tk.LabelFrame(content_frame, text="📝 ACTIVITY LOG", 
                                fg='#00ff00', bg='#2a2a2a', 
                                font=('Arial', 12, 'bold'))
        log_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        self.log_text = tk.Text(log_frame, height=8, bg='#1a1a1a', 
                               fg='#00ff00', font=('Consolas', 10))
        self.log_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Scrollbar for log
        scrollbar = tk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side='right', fill='y')
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # Initial log messages
        self.log("🎯 Ultimate AI Aimbot GUI initialized")
        self.log("💡 Select an engine above to start")
        self.log("⚡ Lightning Fast Learner: Best for most users")
        self.log("💻 CPU Optimized: No GPU required")
        self.log("🚀 Ultimate Speed: Maximum performance with GPU")
        
    def log(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def launch_engine(self, engine_file):
        """Launch selected aimbot engine"""
        if self.running:
            self.log("⚠️ Stopping current aimbot...")
            self.stop_aimbot()
            time.sleep(1)
        
        try:
            engine_path = os.path.join("AI-Aimbot-main", engine_file)
            if not os.path.exists(engine_path):
                self.log(f"❌ Engine file not found: {engine_file}")
                return
                
            self.log(f"🚀 Launching {engine_file}...")
            self.status_label.config(text="🟢 STARTING...", fg='#ffaa00')
            
            # Launch in new process
            self.current_process = subprocess.Popen([
                "python", engine_path
            ], cwd=os.getcwd())
            
            self.running = True
            self.status_label.config(text=f"🟢 RUNNING - {engine_file}", fg='#00ff00')
            self.log(f"✅ {engine_file} started successfully")
            
            # Start monitoring thread
            monitor_thread = threading.Thread(target=self.monitor_process, daemon=True)
            monitor_thread.start()
            
        except Exception as e:
            self.log(f"❌ Failed to launch {engine_file}: {str(e)}")
            self.status_label.config(text="❌ FAILED TO START", fg='#ff0000')
            
    def monitor_process(self):
        """Monitor the running process"""
        while self.running and self.current_process:
            if self.current_process.poll() is not None:
                # Process ended
                self.running = False
                self.status_label.config(text="⚫ OFFLINE - Process ended", fg='#ff6600')
                self.log("🛑 Aimbot process ended")
                break
            time.sleep(1)
            
    def stop_aimbot(self):
        """Stop the current aimbot"""
        if self.current_process:
            try:
                self.current_process.terminate()
                self.current_process.wait(timeout=5)
                self.log("🛑 Aimbot stopped")
            except subprocess.TimeoutExpired:
                self.current_process.kill()
                self.log("🛑 Aimbot force killed")
            except Exception as e:
                self.log(f"⚠️ Error stopping aimbot: {str(e)}")
                
            self.current_process = None
            
        self.running = False
        self.status_label.config(text="⚫ OFFLINE", fg='#ff6600')
        
    def install_dependencies(self):
        """Install required dependencies"""
        self.log("📦 Installing dependencies...")
        self.status_label.config(text="📦 INSTALLING DEPENDENCIES...", fg='#ffaa00')
        
        def install_thread():
            try:
                # Install PyTorch with CUDA
                subprocess.run([
                    "pip", "install", "--upgrade", 
                    "torch", "torchvision", "torchaudio", 
                    "--index-url", "https://download.pytorch.org/whl/cu118"
                ], check=True)
                
                # Install other dependencies
                subprocess.run([
                    "pip", "install", "--upgrade",
                    "opencv-python", "numpy", "mss", "pillow", 
                    "pywin32", "psutil", "numba", "cupy-cuda11x"
                ], check=True)
                
                self.log("✅ All dependencies installed successfully!")
                self.status_label.config(text="✅ DEPENDENCIES INSTALLED", fg='#00ff00')
                
            except subprocess.CalledProcessError as e:
                self.log(f"❌ Failed to install dependencies: {str(e)}")
                self.status_label.config(text="❌ INSTALLATION FAILED", fg='#ff0000')
            except Exception as e:
                self.log(f"❌ Unexpected error: {str(e)}")
                self.status_label.config(text="❌ INSTALLATION FAILED", fg='#ff0000')
        
        # Run installation in background thread
        install_thread_obj = threading.Thread(target=install_thread, daemon=True)
        install_thread_obj.start()
        
    def on_closing(self):
        """Handle window closing"""
        if self.running:
            self.stop_aimbot()
        self.root.destroy()
        
    def run(self):
        """Start the GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleAimbotGUI()
    app.run()
