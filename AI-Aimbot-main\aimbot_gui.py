#!/usr/bin/env python3
"""
🎯 ULTIMATE AIMBOT GUI
======================
Professional GUI interface for the AI Aimbot system with:
• Real-time performance monitoring
• Easy configuration controls
• Visual target detection overlay
• Multiple aimbot engine selection
• Advanced settings panel
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import cv2
import numpy as np
import mss
try:
    import win32api
    import win32gui
    import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("⚠️ Win32 not available - using alternative methods")
from PIL import Image, ImageTk
import psutil
import os

class AimbotGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 ULTIMATE AI AIMBOT - Professional Edition")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a1a')
        
        # Set high priority
        try:
            p = psutil.Process(os.getpid())
            p.nice(psutil.HIGH_PRIORITY_CLASS)
        except:
            pass
        
        # Aimbot state
        self.running = False
        self.engine_type = "Lightning Fast"
        self.sensitivity = 0.5
        self.fov_size = 300
        self.detection_threshold = 0.6
        self.smoothing = 0.8
        
        # Performance tracking
        self.fps = 0
        self.latency = 0
        self.detections = 0
        self.movements = 0
        
        # Screen capture
        self.sct = mss.mss()
        
        self.setup_gui()
        self.start_performance_monitor()
        
    def setup_gui(self):
        """Setup the main GUI interface"""
        # Main title
        title_frame = tk.Frame(self.root, bg='#1a1a1a')
        title_frame.pack(fill='x', padx=20, pady=10)
        
        title_label = tk.Label(title_frame, text="🎯 ULTIMATE AI AIMBOT", 
                              font=('Arial', 24, 'bold'), 
                              fg='#00ff00', bg='#1a1a1a')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="Professional Gaming Enhancement System", 
                                 font=('Arial', 12), 
                                 fg='#888888', bg='#1a1a1a')
        subtitle_label.pack()
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Left panel - Controls
        left_panel = tk.Frame(main_frame, bg='#2a2a2a', relief='raised', bd=2)
        left_panel.pack(side='left', fill='y', padx=(0, 10))
        
        self.setup_control_panel(left_panel)
        
        # Right panel - Monitor and Preview
        right_panel = tk.Frame(main_frame, bg='#2a2a2a', relief='raised', bd=2)
        right_panel.pack(side='right', fill='both', expand=True)
        
        self.setup_monitor_panel(right_panel)
        
    def setup_control_panel(self, parent):
        """Setup the control panel"""
        # Control Panel Title
        control_title = tk.Label(parent, text="🎮 CONTROL PANEL", 
                                font=('Arial', 16, 'bold'), 
                                fg='#00ff00', bg='#2a2a2a')
        control_title.pack(pady=10)
        
        # Engine Selection
        engine_frame = tk.LabelFrame(parent, text="Aimbot Engine", 
                                   fg='white', bg='#2a2a2a', 
                                   font=('Arial', 10, 'bold'))
        engine_frame.pack(fill='x', padx=10, pady=5)
        
        self.engine_var = tk.StringVar(value="Lightning Fast")
        engines = ["Lightning Fast", "Ultimate Speed", "CPU Optimized", "Neural Network"]
        
        for engine in engines:
            rb = tk.Radiobutton(engine_frame, text=engine, 
                               variable=self.engine_var, value=engine,
                               fg='white', bg='#2a2a2a', 
                               selectcolor='#404040',
                               command=self.on_engine_change)
            rb.pack(anchor='w', padx=5, pady=2)
        
        # Settings Frame
        settings_frame = tk.LabelFrame(parent, text="Settings", 
                                     fg='white', bg='#2a2a2a', 
                                     font=('Arial', 10, 'bold'))
        settings_frame.pack(fill='x', padx=10, pady=5)
        
        # Sensitivity
        tk.Label(settings_frame, text="Sensitivity:", 
                fg='white', bg='#2a2a2a').pack(anchor='w', padx=5)
        self.sensitivity_var = tk.DoubleVar(value=0.5)
        sensitivity_scale = tk.Scale(settings_frame, from_=0.1, to=2.0, 
                                   resolution=0.1, orient='horizontal',
                                   variable=self.sensitivity_var,
                                   bg='#2a2a2a', fg='white', 
                                   highlightbackground='#2a2a2a')
        sensitivity_scale.pack(fill='x', padx=5, pady=2)
        
        # FOV Size
        tk.Label(settings_frame, text="FOV Size:", 
                fg='white', bg='#2a2a2a').pack(anchor='w', padx=5)
        self.fov_var = tk.IntVar(value=300)
        fov_scale = tk.Scale(settings_frame, from_=100, to=800, 
                           orient='horizontal', variable=self.fov_var,
                           bg='#2a2a2a', fg='white', 
                           highlightbackground='#2a2a2a')
        fov_scale.pack(fill='x', padx=5, pady=2)
        
        # Detection Threshold
        tk.Label(settings_frame, text="Detection Threshold:", 
                fg='white', bg='#2a2a2a').pack(anchor='w', padx=5)
        self.threshold_var = tk.DoubleVar(value=0.6)
        threshold_scale = tk.Scale(settings_frame, from_=0.1, to=1.0, 
                                 resolution=0.1, orient='horizontal',
                                 variable=self.threshold_var,
                                 bg='#2a2a2a', fg='white', 
                                 highlightbackground='#2a2a2a')
        threshold_scale.pack(fill='x', padx=5, pady=2)
        
        # Smoothing
        tk.Label(settings_frame, text="Mouse Smoothing:", 
                fg='white', bg='#2a2a2a').pack(anchor='w', padx=5)
        self.smoothing_var = tk.DoubleVar(value=0.8)
        smoothing_scale = tk.Scale(settings_frame, from_=0.1, to=1.0, 
                                 resolution=0.1, orient='horizontal',
                                 variable=self.smoothing_var,
                                 bg='#2a2a2a', fg='white', 
                                 highlightbackground='#2a2a2a')
        smoothing_scale.pack(fill='x', padx=5, pady=2)
        
        # Control Buttons
        button_frame = tk.Frame(parent, bg='#2a2a2a')
        button_frame.pack(fill='x', padx=10, pady=20)
        
        self.start_button = tk.Button(button_frame, text="🚀 START AIMBOT", 
                                     command=self.toggle_aimbot,
                                     bg='#00aa00', fg='white', 
                                     font=('Arial', 12, 'bold'),
                                     relief='raised', bd=3)
        self.start_button.pack(fill='x', pady=5)
        
        self.calibrate_button = tk.Button(button_frame, text="🎯 CALIBRATE", 
                                         command=self.calibrate_aimbot,
                                         bg='#0066cc', fg='white', 
                                         font=('Arial', 10, 'bold'),
                                         relief='raised', bd=2)
        self.calibrate_button.pack(fill='x', pady=2)
        
        self.test_button = tk.Button(button_frame, text="🧪 TEST MODE", 
                                    command=self.test_mode,
                                    bg='#cc6600', fg='white', 
                                    font=('Arial', 10, 'bold'),
                                    relief='raised', bd=2)
        self.test_button.pack(fill='x', pady=2)
        
    def setup_monitor_panel(self, parent):
        """Setup the monitoring panel"""
        # Monitor Title
        monitor_title = tk.Label(parent, text="📊 PERFORMANCE MONITOR", 
                               font=('Arial', 16, 'bold'), 
                               fg='#00ff00', bg='#2a2a2a')
        monitor_title.pack(pady=10)
        
        # Performance Stats
        stats_frame = tk.Frame(parent, bg='#2a2a2a')
        stats_frame.pack(fill='x', padx=10, pady=5)
        
        # Create performance labels
        self.fps_label = tk.Label(stats_frame, text="FPS: 0", 
                                 font=('Arial', 14, 'bold'), 
                                 fg='#00ff00', bg='#2a2a2a')
        self.fps_label.pack(side='left', padx=10)
        
        self.latency_label = tk.Label(stats_frame, text="Latency: 0ms", 
                                    font=('Arial', 14, 'bold'), 
                                    fg='#ffaa00', bg='#2a2a2a')
        self.latency_label.pack(side='left', padx=10)
        
        self.detections_label = tk.Label(stats_frame, text="Detections: 0", 
                                       font=('Arial', 14, 'bold'), 
                                       fg='#ff6600', bg='#2a2a2a')
        self.detections_label.pack(side='left', padx=10)
        
        # Status indicator
        self.status_label = tk.Label(parent, text="⚫ OFFLINE", 
                                   font=('Arial', 18, 'bold'), 
                                   fg='#ff0000', bg='#2a2a2a')
        self.status_label.pack(pady=10)
        
        # Preview Frame
        preview_frame = tk.LabelFrame(parent, text="Live Preview", 
                                    fg='white', bg='#2a2a2a', 
                                    font=('Arial', 12, 'bold'))
        preview_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.preview_label = tk.Label(preview_frame, text="Preview will appear here when aimbot is active", 
                                    fg='#888888', bg='#2a2a2a')
        self.preview_label.pack(expand=True)
        
        # Log Frame
        log_frame = tk.LabelFrame(parent, text="Activity Log", 
                                fg='white', bg='#2a2a2a', 
                                font=('Arial', 10, 'bold'))
        log_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        self.log_text = tk.Text(log_frame, height=6, bg='#1a1a1a', 
                               fg='#00ff00', font=('Consolas', 9))
        self.log_text.pack(fill='x', padx=5, pady=5)
        
        # Add initial log message
        self.log("🎯 Ultimate AI Aimbot GUI initialized")
        self.log("💡 Select engine and click START to begin")
        
    def log(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        
    def on_engine_change(self):
        """Handle engine selection change"""
        engine = self.engine_var.get()
        self.log(f"🔧 Engine changed to: {engine}")
        
    def toggle_aimbot(self):
        """Start/stop the aimbot"""
        if not self.running:
            self.start_aimbot()
        else:
            self.stop_aimbot()
            
    def start_aimbot(self):
        """Start the aimbot"""
        self.running = True
        self.start_button.config(text="🛑 STOP AIMBOT", bg='#aa0000')
        self.status_label.config(text="🟢 ONLINE", fg='#00ff00')
        
        engine = self.engine_var.get()
        self.log(f"🚀 Starting {engine} engine...")
        self.log(f"⚙️ Sensitivity: {self.sensitivity_var.get()}")
        self.log(f"⚙️ FOV: {self.fov_var.get()}px")
        self.log(f"⚙️ Threshold: {self.threshold_var.get()}")
        
        # Start aimbot thread
        self.aimbot_thread = threading.Thread(target=self.aimbot_loop, daemon=True)
        self.aimbot_thread.start()
        
    def stop_aimbot(self):
        """Stop the aimbot"""
        self.running = False
        self.start_button.config(text="🚀 START AIMBOT", bg='#00aa00')
        self.status_label.config(text="⚫ OFFLINE", fg='#ff0000')
        self.log("🛑 Aimbot stopped")
        
    def calibrate_aimbot(self):
        """Calibrate the aimbot"""
        self.log("🎯 Starting calibration...")
        messagebox.showinfo("Calibration", "Move your mouse to different targets for 10 seconds")
        
    def test_mode(self):
        """Enable test mode"""
        self.log("🧪 Test mode activated - no mouse movement")
        
    def aimbot_loop(self):
        """Main aimbot processing loop"""
        while self.running:
            start_time = time.perf_counter()
            
            try:
                # Get screen region
                if WIN32_AVAILABLE:
                    screen_w = win32api.GetSystemMetrics(0)
                    screen_h = win32api.GetSystemMetrics(1)
                else:
                    screen_w, screen_h = 1920, 1080  # Default resolution

                fov = self.fov_var.get()

                # Capture center region
                region = {
                    'left': screen_w // 2 - fov // 2,
                    'top': screen_h // 2 - fov // 2,
                    'width': fov,
                    'height': fov
                }

                screenshot = self.sct.grab(region)
                frame = np.array(screenshot)
                frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
                
                # Simple color-based detection
                targets = self.detect_targets(frame)
                
                if targets:
                    self.detections += 1
                    best_target = max(targets, key=lambda t: t['confidence'])
                    
                    # Move mouse to target
                    target_x = region['left'] + best_target['x']
                    target_y = region['top'] + best_target['y']

                    if WIN32_AVAILABLE:
                        current_x, current_y = win32gui.GetCursorPos()

                        # Apply smoothing
                        smoothing = self.smoothing_var.get()
                        sensitivity = self.sensitivity_var.get()

                        dx = (target_x - current_x) * sensitivity * smoothing
                        dy = (target_y - current_y) * sensitivity * smoothing

                        new_x = int(current_x + dx)
                        new_y = int(current_y + dy)

                        win32api.SetCursorPos((new_x, new_y))
                        self.movements += 1
                    else:
                        self.log("⚠️ Mouse movement disabled - Win32 not available")
                
                # Update performance
                self.latency = (time.perf_counter() - start_time) * 1000
                
            except Exception as e:
                self.log(f"❌ Error: {str(e)}")
                
            time.sleep(0.001)  # Small delay
            
    def detect_targets(self, frame):
        """Simple target detection"""
        targets = []
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Define enemy color ranges
        color_ranges = [
            ([0, 100, 100], [10, 255, 255]),    # Red
            ([100, 100, 100], [130, 255, 255]), # Blue
            ([20, 100, 100], [30, 255, 255])    # Yellow
        ]
        
        for lower, upper in color_ranges:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 100:  # Minimum target size
                    x, y, w, h = cv2.boundingRect(contour)
                    confidence = min(area / 1000.0, 1.0)
                    
                    if confidence > self.threshold_var.get():
                        targets.append({
                            'x': x + w // 2,
                            'y': y + h // 2,
                            'confidence': confidence
                        })
        
        return targets
        
    def start_performance_monitor(self):
        """Start performance monitoring"""
        def update_stats():
            if self.running:
                self.fps = min(120, 1000 / max(self.latency, 1))
                
            self.fps_label.config(text=f"FPS: {self.fps:.0f}")
            self.latency_label.config(text=f"Latency: {self.latency:.1f}ms")
            self.detections_label.config(text=f"Detections: {self.detections}")
            
            self.root.after(100, update_stats)
            
        update_stats()
        
    def run(self):
        """Start the GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = AimbotGUI()
    app.run()
