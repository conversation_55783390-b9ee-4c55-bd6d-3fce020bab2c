"""
System Integration Manager
Coordinates all technical components described in AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md
Provides unified interface for display detection, screen capture, and input simulation
"""

import time
import threading
from dataclasses import dataclass
from enum import Enum
from typing import Optional, Dict, Any, Callable
import numpy as np

from .display_mode_detector import DisplayModeDetector, DisplayMode, DisplayInfo
from .enhanced_screen_capture import EnhancedScreenCapture, CaptureMethod
from .input_method_manager import InputMethodManager, InputMethod, InputConfig
from .performance_optimizer import PerformanceOptimizer


class SystemStatus(Enum):
    """System integration status"""
    INITIALIZING = "initializing"
    READY = "ready"
    RUNNING = "running"
    ERROR = "error"
    SHUTDOWN = "shutdown"


@dataclass
class SystemConfiguration:
    """System-wide configuration"""
    # Display settings
    auto_detect_display_mode: bool = True
    preferred_capture_method: Optional[CaptureMethod] = None
    
    # Input settings
    input_method: InputMethod = InputMethod.WIN32_SENDINPUT
    enable_humanization: bool = True
    
    # Performance settings
    enable_performance_monitoring: bool = True
    auto_optimize: bool = True
    target_fps: float = 60.0
    
    # Integration settings
    compatibility_check_interval: float = 5.0
    auto_fallback: bool = True


class SystemIntegrationManager:
    """
    Manages integration between all system components
    Implements technical architecture from AIMBOT_INTEGRATION_TECHNICAL_REFERENCE.md
    """
    
    def __init__(self, config: SystemConfiguration, logger):
        self.config = config
        self.logger = logger
        self.status = SystemStatus.INITIALIZING
        
        # Component managers
        self.display_detector: Optional[DisplayModeDetector] = None
        self.screen_capture: Optional[EnhancedScreenCapture] = None
        self.input_manager: Optional[InputMethodManager] = None
        self.performance_optimizer: Optional[PerformanceOptimizer] = None
        
        # System state
        self.current_display_info: Optional[DisplayInfo] = None
        self.compatibility_status = {}
        self.last_compatibility_check = 0.0
        
        # Monitoring threads
        self.monitoring_thread: Optional[threading.Thread] = None
        self.monitoring_active = False
        
        # Callbacks
        self.status_change_callback: Optional[Callable] = None
        self.compatibility_change_callback: Optional[Callable] = None
        
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all system components"""
        try:
            self.logger.info("Initializing system integration components...")
            
            # Initialize display mode detector
            self.display_detector = DisplayModeDetector(self.logger)
            self.logger.info("Display mode detector initialized")
            
            # Initialize screen capture system
            self.screen_capture = EnhancedScreenCapture(self.logger)
            self.logger.info("Enhanced screen capture initialized")
            
            # Initialize input method manager
            input_config = InputConfig(
                method=self.config.input_method,
                humanization_enabled=self.config.enable_humanization
            )
            self.input_manager = InputMethodManager(input_config, self.logger)
            self.logger.info("Input method manager initialized")
            
            # Initialize performance optimizer
            if self.config.enable_performance_monitoring:
                self.performance_optimizer = PerformanceOptimizer(self.logger)
                self.performance_optimizer.start_monitoring()
                self.logger.info("Performance optimizer initialized")
            
            # Perform initial compatibility check
            self._perform_compatibility_check()
            
            self.status = SystemStatus.READY
            self.logger.info("System integration manager ready")
            
        except Exception as e:
            self.logger.error(f"System initialization failed: {e}")
            self.status = SystemStatus.ERROR
            raise
    
    def start_monitoring(self):
        """Start system monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        self.logger.info("System monitoring started")
    
    def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
        self.logger.info("System monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                current_time = time.time()
                
                # Periodic compatibility check
                if (current_time - self.last_compatibility_check) >= self.config.compatibility_check_interval:
                    self._perform_compatibility_check()
                    self.last_compatibility_check = current_time
                
                # Auto-optimization
                if self.config.auto_optimize and self.performance_optimizer:
                    self._perform_auto_optimization()
                
                time.sleep(1.0)  # Check every second
                
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                time.sleep(5.0)  # Wait before retrying
    
    def _perform_compatibility_check(self):
        """Perform comprehensive compatibility check"""
        try:
            # Check display mode compatibility
            if self.display_detector:
                display_info = self.display_detector.detect_foreground_display_mode()
                
                # Check if display mode changed
                if (not self.current_display_info or 
                    self.current_display_info.mode != display_info.mode):
                    
                    self.current_display_info = display_info
                    self.logger.info(f"Display mode: {display_info.mode.value}")
                    
                    # Update compatibility status
                    self._update_compatibility_status(display_info)
                    
                    # Trigger callback if registered
                    if self.compatibility_change_callback:
                        self.compatibility_change_callback(display_info)
            
            # Check screen capture compatibility
            if self.screen_capture and self.current_display_info:
                self._test_screen_capture_compatibility()
            
            # Check input method compatibility
            if self.input_manager:
                self._test_input_method_compatibility()
            
        except Exception as e:
            self.logger.error(f"Compatibility check failed: {e}")
    
    def _update_compatibility_status(self, display_info: DisplayInfo):
        """Update system compatibility status based on display mode"""
        
        self.compatibility_status = {
            'display_mode': display_info.mode.value,
            'capture_compatible': display_info.is_capture_compatible,
            'performance_impact': display_info.performance_impact,
            'recommendations': display_info.recommendations.copy()
        }
        
        # Auto-adjust capture method based on display mode
        if self.config.auto_fallback and self.screen_capture:
            if display_info.mode == DisplayMode.FULLSCREEN_EXCLUSIVE:
                # Try hardware-level capture methods first
                self.logger.warning("Fullscreen exclusive detected - screen capture may fail")
                # Could implement hardware capture fallbacks here
            elif display_info.mode == DisplayMode.BORDERLESS_WINDOWED:
                # Optimal mode - use fastest capture method
                optimal_method = self.screen_capture.get_optimal_method()
                self.screen_capture.current_method = optimal_method
                self.logger.info(f"Using optimal capture method: {optimal_method.value}")
    
    def _test_screen_capture_compatibility(self):
        """Test screen capture functionality"""
        try:
            # Capture small test region
            test_frame = self.screen_capture.capture_screen_region(0, 0, 100, 100)
            
            if test_frame is not None:
                self.compatibility_status['screen_capture'] = 'working'
            else:
                self.compatibility_status['screen_capture'] = 'failed'
                self.logger.warning("Screen capture test failed")
                
                # Try fallback methods if auto-fallback enabled
                if self.config.auto_fallback:
                    self._try_capture_fallbacks()
                    
        except Exception as e:
            self.logger.error(f"Screen capture test failed: {e}")
            self.compatibility_status['screen_capture'] = 'error'
    
    def _try_capture_fallbacks(self):
        """Try fallback capture methods"""
        for method in [CaptureMethod.WIN32_BITBLT, CaptureMethod.WIN32_PRINTWINDOW, CaptureMethod.PIL_IMAGEGRAB]:
            try:
                test_frame = self.screen_capture._capture_with_method(method, 0, 0, 100, 100)
                if test_frame is not None:
                    self.screen_capture.current_method = method
                    self.logger.info(f"Switched to fallback capture method: {method.value}")
                    self.compatibility_status['screen_capture'] = 'working'
                    return
            except Exception as e:
                self.logger.debug(f"Fallback method {method.value} failed: {e}")
        
        self.logger.error("All capture methods failed")
    
    def _test_input_method_compatibility(self):
        """Test input method functionality"""
        try:
            # Test current input method with minimal movement
            success = self.input_manager._execute_input_method(1, 1)
            
            if success:
                self.compatibility_status['input_method'] = 'working'
            else:
                self.compatibility_status['input_method'] = 'failed'
                self.logger.warning("Input method test failed")
                
        except Exception as e:
            self.logger.error(f"Input method test failed: {e}")
            self.compatibility_status['input_method'] = 'error'
    
    def _perform_auto_optimization(self):
        """Perform automatic system optimization"""
        if not self.performance_optimizer:
            return
        
        try:
            # Get current performance metrics
            metrics = self.performance_optimizer.metrics
            
            # Auto-adjust based on performance
            if metrics.avg_fps < self.config.target_fps * 0.8:  # 20% below target
                self._optimize_for_performance()
            elif metrics.avg_fps > self.config.target_fps * 1.2:  # 20% above target
                self._optimize_for_quality()
                
        except Exception as e:
            self.logger.error(f"Auto-optimization failed: {e}")
    
    def _optimize_for_performance(self):
        """Optimize system for better performance"""
        self.logger.info("Optimizing for performance")
        
        # Switch to faster capture method if available
        if self.screen_capture:
            if CaptureMethod.MSS in self.screen_capture.capture_methods:
                self.screen_capture.current_method = CaptureMethod.MSS
        
        # Reduce input humanization for speed
        if self.input_manager and self.input_manager.config.humanization_enabled:
            self.input_manager.config.movement_smoothing = max(3, self.input_manager.config.movement_smoothing - 1)
    
    def _optimize_for_quality(self):
        """Optimize system for better quality"""
        self.logger.info("Optimizing for quality")
        
        # Increase input humanization
        if self.input_manager and self.input_manager.config.humanization_enabled:
            self.input_manager.config.movement_smoothing = min(10, self.input_manager.config.movement_smoothing + 1)
    
    def capture_screen_region(self, x: int, y: int, width: int, height: int) -> Optional[np.ndarray]:
        """Capture screen region using integrated system"""
        if not self.screen_capture:
            return None
        
        frame = self.screen_capture.capture_screen_region(x, y, width, height)
        
        # Update performance metrics if available
        if self.performance_optimizer and frame is not None:
            capture_time = time.perf_counter()  # This would need proper timing
            self.performance_optimizer.update_frame_metrics(0.016, 0.010, 0.005)  # Example values
        
        return frame
    
    def move_to_target(self, target_x: int, target_y: int, current_x: int, current_y: int) -> bool:
        """Move to target using integrated input system"""
        if not self.input_manager:
            return False
        
        return self.input_manager.move_to_target(target_x, target_y, current_x, current_y)
    
    def get_system_status_report(self) -> str:
        """Generate comprehensive system status report"""
        report = f"""
=== SYSTEM INTEGRATION STATUS ===
Status: {self.status.value.upper()}
Display Mode: {self.current_display_info.mode.value if self.current_display_info else 'Unknown'}
Capture Compatible: {'YES' if self.compatibility_status.get('capture_compatible', False) else 'NO'}

=== COMPONENT STATUS ===
Display Detector: {'OK' if self.display_detector else 'FAILED'}
Screen Capture: {'OK' if self.screen_capture else 'FAILED'}
Input Manager: {'OK' if self.input_manager else 'FAILED'}
Performance Optimizer: {'OK' if self.performance_optimizer else 'DISABLED'}

=== COMPATIBILITY STATUS ===
"""
        
        for key, value in self.compatibility_status.items():
            report += f"{key.replace('_', ' ').title()}: {str(value).upper()}\n"
        
        if self.performance_optimizer:
            report += f"\n{self.performance_optimizer.get_performance_report()}"
        
        if self.screen_capture:
            report += f"\n{self.screen_capture.get_performance_report()}"
        
        return report
    
    def set_status_change_callback(self, callback: Callable):
        """Set callback for status changes"""
        self.status_change_callback = callback
    
    def set_compatibility_change_callback(self, callback: Callable):
        """Set callback for compatibility changes"""
        self.compatibility_change_callback = callback
    
    def shutdown(self):
        """Shutdown system integration manager"""
        self.logger.info("Shutting down system integration manager")
        self.status = SystemStatus.SHUTDOWN
        
        # Stop monitoring
        self.stop_monitoring()
        
        # Cleanup components
        if self.performance_optimizer:
            self.performance_optimizer.stop_monitoring()
        
        if self.screen_capture:
            self.screen_capture.cleanup()
        
        self.logger.info("System integration manager shutdown complete")
