#!/usr/bin/env python3
"""
🚀 AIMBOT LAUNCHER
==================
Easy launcher for the visual aimbot with settings GUI
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import threading
import sys
import os

class AimbotLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Aimbot Launcher")
        self.root.geometry("500x400")
        self.root.configure(bg='#2b2b2b')
        
        # Process tracking
        self.aimbot_process = None
        self.gui_process = None
        
        self.create_widgets()
        
    def create_widgets(self):
        """Create launcher GUI"""
        # Title
        title_label = tk.Label(self.root, text="🎯 ULTIMATE AI AIMBOT", 
                              font=('Arial', 18, 'bold'), 
                              bg='#2b2b2b', fg='white')
        title_label.pack(pady=20)
        
        # Subtitle
        subtitle_label = tk.Label(self.root, text="Choose your launch option:", 
                                 font=('Arial', 12), 
                                 bg='#2b2b2b', fg='lightgray')
        subtitle_label.pack(pady=10)
        
        # Launch options frame
        options_frame = tk.Frame(self.root, bg='#2b2b2b')
        options_frame.pack(pady=20)
        
        # Option 1: Aimbot only
        btn1 = tk.Button(options_frame, text="🎯 Launch Aimbot Only", 
                        font=('Arial', 12, 'bold'),
                        bg='#4CAF50', fg='white',
                        width=25, height=2,
                        command=self.launch_aimbot_only)
        btn1.pack(pady=10)
        
        # Option 2: Aimbot with Settings GUI
        btn2 = tk.Button(options_frame, text="🎛️ Launch with Settings GUI", 
                        font=('Arial', 12, 'bold'),
                        bg='#2196F3', fg='white',
                        width=25, height=2,
                        command=self.launch_with_gui)
        btn2.pack(pady=10)
        
        # Option 3: Settings GUI only
        btn3 = tk.Button(options_frame, text="⚙️ Settings GUI Only", 
                        font=('Arial', 12, 'bold'),
                        bg='#FF9800', fg='white',
                        width=25, height=2,
                        command=self.launch_gui_only)
        btn3.pack(pady=10)
        
        # Status frame
        status_frame = tk.Frame(self.root, bg='#2b2b2b')
        status_frame.pack(pady=20, fill='x', padx=20)
        
        self.status_label = tk.Label(status_frame, text="Ready to launch", 
                                    font=('Arial', 10), 
                                    bg='#2b2b2b', fg='lightgray')
        self.status_label.pack()
        
        # Control buttons
        control_frame = tk.Frame(self.root, bg='#2b2b2b')
        control_frame.pack(pady=10)
        
        stop_btn = tk.Button(control_frame, text="🛑 Stop All", 
                           font=('Arial', 10),
                           bg='#f44336', fg='white',
                           command=self.stop_all)
        stop_btn.pack(side='left', padx=5)
        
        exit_btn = tk.Button(control_frame, text="❌ Exit", 
                           font=('Arial', 10),
                           bg='#9E9E9E', fg='white',
                           command=self.exit_launcher)
        exit_btn.pack(side='left', padx=5)
        
        # Instructions
        instructions = tk.Text(self.root, height=6, width=60, 
                              bg='#3b3b3b', fg='lightgray',
                              font=('Consolas', 9))
        instructions.pack(pady=10, padx=20)
        
        instructions.insert('1.0', """
🎯 AIMBOT CONTROLS:
• F1: Toggle aimbot ON/OFF (starts inactive)
• F2: Open settings GUI (if not already open)
• Q: Quit aimbot
• S: Open settings GUI from aimbot window

⚠️  IMPORTANT: Aimbot starts INACTIVE for safety!
   Press F1 to activate mouse movement.
        """)
        instructions.config(state='disabled')
        
    def launch_aimbot_only(self):
        """Launch aimbot without GUI"""
        try:
            self.status_label.config(text="🚀 Launching aimbot...", fg='yellow')
            self.root.update()
            
            self.aimbot_process = subprocess.Popen([sys.executable, 'visual_aimbot_overlay.py'])
            self.status_label.config(text="✅ Aimbot running (PID: {})".format(self.aimbot_process.pid), fg='green')
        except Exception as e:
            self.status_label.config(text=f"❌ Failed to launch: {e}", fg='red')
            messagebox.showerror("Error", f"Failed to launch aimbot: {e}")
    
    def launch_with_gui(self):
        """Launch aimbot with settings GUI"""
        try:
            self.status_label.config(text="🚀 Launching aimbot with GUI...", fg='yellow')
            self.root.update()
            
            # Launch aimbot with GUI flag
            self.aimbot_process = subprocess.Popen([sys.executable, 'visual_aimbot_overlay.py', '--with-gui'])
            self.status_label.config(text="✅ Aimbot + GUI running (PID: {})".format(self.aimbot_process.pid), fg='green')
        except Exception as e:
            self.status_label.config(text=f"❌ Failed to launch: {e}", fg='red')
            messagebox.showerror("Error", f"Failed to launch: {e}")
    
    def launch_gui_only(self):
        """Launch settings GUI only"""
        try:
            self.status_label.config(text="🚀 Launching settings GUI...", fg='yellow')
            self.root.update()
            
            self.gui_process = subprocess.Popen([sys.executable, 'aimbot_settings_gui.py'])
            self.status_label.config(text="✅ Settings GUI running (PID: {})".format(self.gui_process.pid), fg='green')
        except Exception as e:
            self.status_label.config(text=f"❌ Failed to launch GUI: {e}", fg='red')
            messagebox.showerror("Error", f"Failed to launch GUI: {e}")
    
    def stop_all(self):
        """Stop all running processes"""
        stopped = []
        
        if self.aimbot_process and self.aimbot_process.poll() is None:
            self.aimbot_process.terminate()
            stopped.append("Aimbot")
            
        if self.gui_process and self.gui_process.poll() is None:
            self.gui_process.terminate()
            stopped.append("Settings GUI")
        
        if stopped:
            self.status_label.config(text=f"🛑 Stopped: {', '.join(stopped)}", fg='orange')
        else:
            self.status_label.config(text="ℹ️ No processes to stop", fg='gray')
    
    def exit_launcher(self):
        """Exit launcher"""
        self.stop_all()
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """Run the launcher"""
        self.root.protocol("WM_DELETE_WINDOW", self.exit_launcher)
        self.root.mainloop()

if __name__ == "__main__":
    launcher = AimbotLauncher()
    launcher.run()
