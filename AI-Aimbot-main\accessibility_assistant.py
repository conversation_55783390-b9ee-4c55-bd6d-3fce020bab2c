#!/usr/bin/env python3
"""
Gaming Accessibility Assistant
Designed to help individuals with motor control difficulties (cerebral palsy, etc.)
Uses legitimate Windows accessibility APIs and assistive technologies
"""

import ctypes
import ctypes.wintypes
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox
import cv2
import numpy as np
from dataclasses import dataclass
from enum import Enum
from typing import Optional, Tuple, Callable
import speech_recognition as sr
import pyttsx3

# Windows accessibility APIs
from ctypes import wintypes
import win32api
import win32con
import win32gui


class AssistanceLevel(Enum):
    """Levels of assistance for different motor control abilities"""
    MINIMAL = "minimal"      # Slight assistance with precision
    MODERATE = "moderate"    # Significant movement assistance
    HIGH = "high"           # Major assistance with all movements
    FULL = "full"           # Complete automation with user intent


@dataclass
class AccessibilityConfig:
    """Configuration for accessibility features"""
    assistance_level: AssistanceLevel = AssistanceLevel.MODERATE
    
    # Motor assistance settings
    movement_smoothing: float = 0.8  # 0.0 = no smoothing, 1.0 = maximum smoothing
    click_assistance: bool = True    # Auto-click when hovering
    dwell_time: float = 1.5         # Seconds to hover before auto-click
    
    # Visual assistance
    target_highlighting: bool = True  # Highlight detected targets
    crosshair_assistance: bool = True # Show enhanced crosshair
    
    # Input methods
    eye_tracking_enabled: bool = False
    voice_commands_enabled: bool = True
    head_tracking_enabled: bool = False
    
    # Safety features
    max_assistance_time: float = 300.0  # 5 minutes max continuous assistance
    require_confirmation: bool = True    # Require user confirmation for actions


class AccessibilityAssistant:
    """
    Gaming accessibility assistant for individuals with motor control difficulties
    Uses legitimate Windows APIs and accessibility frameworks
    """
    
    def __init__(self, config: AccessibilityConfig):
        self.config = config
        self.running = False
        self.assistance_active = False
        self.last_user_input = time.time()
        
        # UI components
        self.root = None
        self.status_label = None
        self.assistance_button = None
        
        # Voice recognition
        self.voice_recognizer = None
        self.voice_engine = None
        
        # Target detection (using existing computer vision)
        self.target_detector = None
        
        # Accessibility state
        self.current_target = None
        self.hover_start_time = None
        self.assistance_start_time = None
        
        self._initialize_accessibility_features()
    
    def _initialize_accessibility_features(self):
        """Initialize accessibility features"""
        
        # Initialize voice recognition if enabled
        if self.config.voice_commands_enabled:
            try:
                self.voice_recognizer = sr.Recognizer()
                self.voice_engine = pyttsx3.init()
                self.voice_engine.setProperty('rate', 150)  # Slower speech rate
                print("✅ Voice recognition initialized")
            except Exception as e:
                print(f"⚠️ Voice recognition failed to initialize: {e}")
        
        # Initialize target detection (reuse existing computer vision)
        try:
            from aimbot import EnhancedAimbot, AimbotConfig
            aimbot_config = AimbotConfig(confidence_threshold=0.3)  # Lower threshold for accessibility
            self.target_detector = EnhancedAimbot(aimbot_config)
            print("✅ Target detection initialized")
        except Exception as e:
            print(f"⚠️ Target detection failed to initialize: {e}")
    
    def create_accessibility_ui(self):
        """Create accessibility control interface"""
        self.root = tk.Tk()
        self.root.title("Gaming Accessibility Assistant")
        self.root.geometry("400x500")
        self.root.attributes('-topmost', True)  # Keep on top
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Gaming Accessibility Assistant", 
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Status
        self.status_label = ttk.Label(main_frame, text="Ready", 
                                     font=('Arial', 12))
        self.status_label.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        # Assistance level
        ttk.Label(main_frame, text="Assistance Level:").grid(row=2, column=0, sticky=tk.W)
        assistance_var = tk.StringVar(value=self.config.assistance_level.value)
        assistance_combo = ttk.Combobox(main_frame, textvariable=assistance_var,
                                       values=[level.value for level in AssistanceLevel])
        assistance_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        assistance_combo.bind('<<ComboboxSelected>>', self._on_assistance_level_changed)
        
        # Movement smoothing
        ttk.Label(main_frame, text="Movement Smoothing:").grid(row=3, column=0, sticky=tk.W, pady=(10, 0))
        smoothing_var = tk.DoubleVar(value=self.config.movement_smoothing)
        smoothing_scale = ttk.Scale(main_frame, from_=0.0, to=1.0, variable=smoothing_var,
                                   orient=tk.HORIZONTAL, command=self._on_smoothing_changed)
        smoothing_scale.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(10, 0))
        
        # Dwell time
        ttk.Label(main_frame, text="Auto-click Delay (seconds):").grid(row=4, column=0, sticky=tk.W, pady=(10, 0))
        dwell_var = tk.DoubleVar(value=self.config.dwell_time)
        dwell_scale = ttk.Scale(main_frame, from_=0.5, to=3.0, variable=dwell_var,
                               orient=tk.HORIZONTAL, command=self._on_dwell_time_changed)
        dwell_scale.grid(row=4, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(10, 0))
        
        # Feature checkboxes
        features_frame = ttk.LabelFrame(main_frame, text="Assistance Features", padding="10")
        features_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 0))
        
        self.click_assistance_var = tk.BooleanVar(value=self.config.click_assistance)
        ttk.Checkbutton(features_frame, text="Auto-click assistance", 
                       variable=self.click_assistance_var).grid(row=0, column=0, sticky=tk.W)
        
        self.target_highlighting_var = tk.BooleanVar(value=self.config.target_highlighting)
        ttk.Checkbutton(features_frame, text="Target highlighting", 
                       variable=self.target_highlighting_var).grid(row=1, column=0, sticky=tk.W)
        
        self.voice_commands_var = tk.BooleanVar(value=self.config.voice_commands_enabled)
        ttk.Checkbutton(features_frame, text="Voice commands", 
                       variable=self.voice_commands_var).grid(row=2, column=0, sticky=tk.W)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=2, pady=(20, 0))
        
        self.assistance_button = ttk.Button(button_frame, text="Start Assistance", 
                                           command=self._toggle_assistance)
        self.assistance_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Voice Test", 
                  command=self._test_voice_commands).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Calibrate", 
                  command=self._calibrate_system).pack(side=tk.LEFT)
        
        # Instructions
        instructions_frame = ttk.LabelFrame(main_frame, text="Instructions", padding="10")
        instructions_frame.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 0))
        
        instructions_text = """
1. Adjust assistance level based on your needs
2. Enable desired assistance features
3. Click 'Start Assistance' to begin
4. Use voice commands: "click", "stop", "help"
5. Press ESC key to emergency stop
        """
        ttk.Label(instructions_frame, text=instructions_text.strip(), 
                 justify=tk.LEFT).grid(row=0, column=0, sticky=tk.W)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
    
    def _on_assistance_level_changed(self, event):
        """Handle assistance level change"""
        new_level = AssistanceLevel(event.widget.get())
        self.config.assistance_level = new_level
        self._update_assistance_parameters()
    
    def _on_smoothing_changed(self, value):
        """Handle movement smoothing change"""
        self.config.movement_smoothing = float(value)
    
    def _on_dwell_time_changed(self, value):
        """Handle dwell time change"""
        self.config.dwell_time = float(value)
    
    def _update_assistance_parameters(self):
        """Update assistance parameters based on level"""
        if self.config.assistance_level == AssistanceLevel.MINIMAL:
            self.config.movement_smoothing = 0.3
            self.config.dwell_time = 2.0
        elif self.config.assistance_level == AssistanceLevel.MODERATE:
            self.config.movement_smoothing = 0.6
            self.config.dwell_time = 1.5
        elif self.config.assistance_level == AssistanceLevel.HIGH:
            self.config.movement_smoothing = 0.8
            self.config.dwell_time = 1.0
        elif self.config.assistance_level == AssistanceLevel.FULL:
            self.config.movement_smoothing = 0.9
            self.config.dwell_time = 0.5
    
    def _toggle_assistance(self):
        """Toggle assistance on/off"""
        if self.assistance_active:
            self._stop_assistance()
        else:
            self._start_assistance()
    
    def _start_assistance(self):
        """Start accessibility assistance"""
        if self.config.require_confirmation:
            result = messagebox.askyesno("Confirm", 
                                       "Start gaming accessibility assistance?\n\n"
                                       "This will help with mouse movement and clicking.\n"
                                       "Press ESC key to stop at any time.")
            if not result:
                return
        
        self.assistance_active = True
        self.assistance_start_time = time.time()
        self.assistance_button.config(text="Stop Assistance")
        self.status_label.config(text="Assistance Active")
        
        # Start assistance thread
        assistance_thread = threading.Thread(target=self._assistance_loop, daemon=True)
        assistance_thread.start()
        
        if self.voice_engine:
            self.voice_engine.say("Accessibility assistance started")
            self.voice_engine.runAndWait()
    
    def _stop_assistance(self):
        """Stop accessibility assistance"""
        self.assistance_active = False
        self.assistance_button.config(text="Start Assistance")
        self.status_label.config(text="Ready")
        
        if self.voice_engine:
            self.voice_engine.say("Assistance stopped")
            self.voice_engine.runAndWait()
    
    def _assistance_loop(self):
        """Main assistance loop"""
        while self.assistance_active:
            try:
                # Safety check - stop after max time
                if time.time() - self.assistance_start_time > self.config.max_assistance_time:
                    self._stop_assistance()
                    break
                
                # Check for emergency stop (ESC key)
                if win32api.GetAsyncKeyState(win32con.VK_ESCAPE) & 0x8000:
                    self._stop_assistance()
                    break
                
                # Perform assistance based on level
                self._perform_assistance()
                
                time.sleep(0.016)  # ~60 FPS
                
            except Exception as e:
                print(f"Assistance loop error: {e}")
                time.sleep(0.1)
    
    def _perform_assistance(self):
        """Perform assistance based on current configuration"""
        
        # Get current mouse position
        cursor_pos = win32gui.GetCursorPos()
        
        # Detect targets if target detector is available
        targets = []
        if self.target_detector:
            try:
                # This would integrate with the existing target detection
                # For now, we'll simulate target detection
                pass
            except Exception as e:
                pass
        
        # Apply movement smoothing if user is moving mouse
        if self._detect_user_input():
            self._apply_movement_smoothing(cursor_pos)
        
        # Handle auto-click assistance
        if self.config.click_assistance:
            self._handle_auto_click(cursor_pos, targets)
    
    def _detect_user_input(self) -> bool:
        """Detect if user is actively providing input"""
        # Check for mouse movement or key presses
        # This is a simplified version - real implementation would be more sophisticated
        return True
    
    def _apply_movement_smoothing(self, cursor_pos: Tuple[int, int]):
        """Apply movement smoothing to reduce tremors/jerky movements"""
        if self.config.movement_smoothing > 0:
            # This would implement smoothing algorithms
            # For accessibility, this helps users with motor control issues
            pass
    
    def _handle_auto_click(self, cursor_pos: Tuple[int, int], targets: list):
        """Handle automatic clicking when hovering over targets"""
        # Implement dwell clicking for users who have difficulty with manual clicking
        pass
    
    def _test_voice_commands(self):
        """Test voice command recognition"""
        if not self.voice_recognizer:
            messagebox.showerror("Error", "Voice recognition not available")
            return
        
        try:
            with sr.Microphone() as source:
                self.voice_recognizer.adjust_for_ambient_noise(source)
                messagebox.showinfo("Voice Test", "Say something now...")
                
                audio = self.voice_recognizer.listen(source, timeout=5)
                text = self.voice_recognizer.recognize_google(audio)
                
                messagebox.showinfo("Voice Test Result", f"You said: '{text}'")
                
        except sr.WaitTimeoutError:
            messagebox.showwarning("Voice Test", "No speech detected")
        except Exception as e:
            messagebox.showerror("Voice Test Error", f"Error: {e}")
    
    def _calibrate_system(self):
        """Calibrate the accessibility system for the user"""
        messagebox.showinfo("Calibration", 
                           "Calibration helps customize assistance for your specific needs.\n\n"
                           "This would include:\n"
                           "• Movement range testing\n"
                           "• Reaction time measurement\n"
                           "• Preferred assistance levels\n"
                           "• Voice command training")
    
    def run(self):
        """Run the accessibility assistant"""
        self.running = True
        self.create_accessibility_ui()
        
        print("🏥 Gaming Accessibility Assistant Started")
        print("Designed to help individuals with motor control difficulties")
        print("Uses legitimate Windows accessibility APIs")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("Shutting down accessibility assistant...")
        finally:
            self.running = False
            if self.assistance_active:
                self._stop_assistance()


def main():
    """Main entry point for accessibility assistant"""
    print("🏥 Gaming Accessibility Assistant")
    print("Designed for individuals with cerebral palsy and other motor control difficulties")
    print("=" * 70)
    
    # Create accessibility configuration
    config = AccessibilityConfig(
        assistance_level=AssistanceLevel.MODERATE,
        movement_smoothing=0.6,
        click_assistance=True,
        dwell_time=1.5,
        target_highlighting=True,
        voice_commands_enabled=True,
        require_confirmation=True
    )
    
    # Create and run accessibility assistant
    assistant = AccessibilityAssistant(config)
    assistant.run()


if __name__ == "__main__":
    main()
